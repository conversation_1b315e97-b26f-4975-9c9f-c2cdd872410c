<template>
  <div class="PieChart" ref="pieChartRef"></div>
</template>

<script setup>
import { nextTick, ref, watch, markRaw } from "vue";
import {
  getImg,
  numberToThousands,
  getPercent,
  formatNumber,
} from "@/utils/method";
import * as echarts from "echarts";
const props = defineProps({
  title: {
    type: String,
    default: "总量",
  },
  total: {
    type: Number,
    default: 0,
  },
  labelLength: {
    type: Number,
    default: 80,
  },
  numLength: {
    type: Number,
    default: 20,
  },
  data: {
    type: Array,
    default: () => [],
  },
  center: {
    type: String,
    default: '23.2%'
  }
});
const pieChartRef = ref(null);
const pieChart = ref(null);

nextTick(() => {
  initChart();
});

// 添加对 props.data 的监听
watch(() => props.data, (newData) => {
  nextTick(() => {
    if (pieChart.value) {
      let option1 = {
        tooltip: {
          textStyle: {
            fontSize: 20,
          },
          formatter: (params) => {
            return `${params.name}&nbsp;&nbsp;<strong>${params.value}人</strong>`;
          },
        },
        title: [
          {
            text: "{val|" + formatNumber(props.total) + "}\n{name|" + props.title + "}",
            top: "center",
            left: 140,
            textAlign: "center",
            show: props.total,
            textStyle: {
              rich: {
                name: {
                  width: 120,
                  fontSize: 14,
                  fontWeight: "300",
                  padding: [10, 0],
                  color: "#fff",
                },
                val: {
                  width: 120,
                  fontSize: 32,
                  color: "#fff",
                  fontFamily: "Oswald Medium",
                },
              },
            },
          },
        ],
        grid: {
          top: "0%",
          left: "0%",
          right: "0%",
          bottom: "0%",
          containLabel: true,
        },
        legend: {
          orient: "vertical",
          icon: "circle",
          top: "center",
          left: "45%",
          itemGap: 10,
          itemWidth: 10,
          itemHeight: 10,
          data: newData.map((item) => item.name),
          formatter: (name) => {
            let data = option1.series[0].data;
            let tarValue;
            let tarColor;

            for (let i = 0; i < data.length; i++) {
              if (data[i].name === name) {
                tarValue = data[i].value;
                tarColor = data[i].itemStyle?.color;
              }
            }

            // 为每个数值使用独立的样式名称
            return `{name|${name}}{value|${tarValue}}{unit|人}`;
          },
          textStyle: {
            rich: {
              name: {
                color: "#97acb4",
                fontSize: 14,
                fontWeight: 300,
                width: props.labelLength,
              },
              value: {  // 改用 value 作为样式名
                fontSize: 20,
                fontFamily: "Oswald Medium",
                width: props.numLength,
                color: '#fff',
              },
              unit: {
                width: 20,
                color: "#97acb4",
                fontWeight: 300,
              },
            },
          },
        },
        graphic: {
          elements: [
            {
              type: "image",
              z: 1,
              style: {
                image: props.total
                  ? getImg("project/cir3.png")
                  : getImg("project/cir2.png"),
                width: 120,
                height: 120,
              },
              left: 0,
              top: "center",
            },
          ],
        },
        series: [
          {
            type: "pie",
            radius: ["40%", "50%"],
            center: [50, "50%"],
            startAngle: 90,
            avoidLabelOverlap: false,
            data: createDataWithGaps(newData),
            label: { show: false },
            labelLine: { show: false },
          },
          // 内层半透明部分 - 调整宽度为当前的2/3
          {
            type: "pie",
            radius: ["35%", "50%"],
            center: [60, "50%"],
            startAngle: 90,
            clockwise: true,
            silent: true,
            itemStyle: {
              borderWidth: 0,
            },
            data: createDataWithGaps(newData, true),
            label: { show: false },
            labelLine: { show: false },
          },
        ],
      };
      pieChart.value.setOption(option1);
    }
  });
}, { deep: true });

const createDataWithGaps = (data, isTransparent = false) => {
  const result = [];
  // 计算总和
  const total = data.reduce((sum, item) => sum + item.value, 0);
  // 设置固定的间隔比例为0.1%
  const gapSize = total * 0.001;

  data.forEach((item) => {
    // 添加实际数据
    result.push({
      ...item,
      itemStyle: {
        color: isTransparent
          ? item.itemStyle.color.replace("1)", "0.2)")
          : item.itemStyle.color,
      },
    });
    // 添加固定大小的间隔
    result.push({
      value: gapSize,
      itemStyle: {
        color: "rgba(0, 0, 0, 0)",
      },
      label: { show: false },
      labelLine: { show: false },
    });
  });
  return result;
};

function initChart() {
  // 确保DOM元素存在再初始化图表
  if (!pieChartRef.value) {
    console.warn('pieChartRef不存在，无法初始化图表');
    return;
  }
  
  try {
    if (!pieChart.value) {
      // 使用try-catch包裹初始化操作
      pieChart.value = markRaw(echarts.init(pieChartRef.value));
    } else {
      pieChart.value.clear();
    }
    
    let option1 = {
      tooltip: {
        textStyle: {
          fontSize: 20,
        },
        formatter: (params) => {
          return `${params.name}&nbsp;&nbsp;<strong>${params.value}人</strong>`;
        },
      },
      title: [
        {
          text:
            "{val|" +
            formatNumber(props.total) +
            "}\n{name|" +
            props.title +
            "}",
          top: "center",
          left: 120,
          textAlign: "center",
          show: props.total,
          textStyle: {
            rich: {
              name: {
                width: 120,
                fontSize: 14,
                fontWeight: "300",
                padding: [10, 0],
                color: "#fff",
              },
              val: {
                width: 120,
                fontSize: 32,
                color: "#fff",
                fontFamily: "Oswald Medium",
              },
            },
          },
        },
      ],
      grid: {
        top: "0%",
        left: "0%",
        right: "0%",
        bottom: "0%",
        containLabel: true,
      },
      legend: {
        orient: "vertical",
        icon: "circle",
        top: "center",
        left: "45%",
        itemGap: 10,
        itemWidth: 10,
        itemHeight: 10,
        data: props.data.map((item) => item.name),
        formatter: (name) => {
          let data = option1.series[0].data;
          let tarValue;
          let tarColor;

          for (let i = 0; i < data.length; i++) {
            if (data[i].name === name) {
              tarValue = data[i].value;
              tarColor = data[i].itemStyle?.color;
            }
          }

          // 为每个数值使用独立的样式名称
          return `{name|${name}}{value|${tarValue}}{unit|人}`;
        },
        textStyle: {
          rich: {
            name: {
              color: "#97acb4",
              fontSize: 14,
              fontWeight: 300,
              width: props.labelLength,
            },
            value: {  // 改用 value 作为样式名
              fontSize: 20,
              fontFamily: "Oswald Medium",
              width: props.numLength,
              color: '#fff',
            },
            unit: {
              width: 30,
              color: "#97acb4",
              fontWeight: 300,
            },
          },
        },
      },
      graphic: {
        elements: [
          {
            type: "image",
            z: 1,
            style: {
              image: props.total
                ? getImg("project/cir3.png")
                : getImg("project/cir2.png"),
              width: 120,
              height: 120,
            },
            left: 0,
            top: 20,
          },
        ],
      },
      series: [
        {
          type: "pie",
          radius: ["40%", "50%"],
          center: [60, "50%"],
          startAngle: 90,
          avoidLabelOverlap: false,
          data: createDataWithGaps(props.data),
          label: { show: false },
          labelLine: { show: false },
        },
        // 内层半透明部分 - 调整宽度为当前的2/3
        {
          type: "pie",
          radius: ["35%", "50%"],
          center: [60, "50%"],
          startAngle: 90,
          clockwise: true,
          silent: true,
          itemStyle: {
            borderWidth: 0,
          },
          data: createDataWithGaps(props.data, true),
          label: { show: false },
          labelLine: { show: false },
        },
      ],
    };
    pieChart.value.setOption(option1);
  } catch (error) {
    console.error('初始化图表失败:', error);
  }
}
</script>

<style lang="scss" scoped>
.PieChart {
  width: 100%;
  height: 100%;
}
</style> 