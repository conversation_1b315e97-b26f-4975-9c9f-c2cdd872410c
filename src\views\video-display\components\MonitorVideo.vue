<template>
  <div ref="monitorVideoGrid" class="video-grid">
    <div class="video-row" v-for="(row, rowIndex) in displayedMonitorRows" :key="rowIndex">
      <div class="video-item" v-for="(monitor, colIndex) in row" :key="monitor.id || `${rowIndex}-${colIndex}`"
        :data-monitor-id="monitor.isEmpty ? undefined : `${monitor.deviceSerial}-${monitor.channelNo}`">
        <div v-if="!monitor.isEmpty" class="monitor-bg">
          <div class="monitor-name" :title="monitor.storeName">
            {{ monitor.storeName }}
          </div>
          
          <!-- 🔥 关键改进：统一的播放器容器处理 -->
          <div v-if="monitor.channelStatus === '1'" class="monitor-player-wrapper">
            
            <!-- 🎬 添加默认占位背景，避免完全黑屏 -->
            <div class="monitor-placeholder" 
                 :class="{ 'hidden': !playerLoadingStates.get(getPlayerId(monitor)) && !playerErrorStates.get(getPlayerId(monitor)) }">
              <div class="placeholder-content">
                <div class="monitor-icon">📹</div>
                <div class="monitor-title">{{ monitor.storeName }}</div>
              </div>
            </div>
            
            <!-- 加载状态 -->
            <div v-if="playerLoadingStates.get(getPlayerId(monitor))" class="player-loading-overlay">
              <div class="loading-spinner-small"></div>
              <div class="loading-text-small">正在连接监控...</div>
            </div>
            
            <!-- 错误状态 -->
            <div v-else-if="playerErrorStates.get(getPlayerId(monitor))" class="player-error-overlay">
              <div class="error-icon">⚠️</div>
              <div class="error-text-small">连接失败</div>
              <button class="retry-btn-small" @click="retryPlayer(monitor)">重试</button>
            </div>
            
            <!-- 播放器容器 - 确保有固定尺寸 -->
            <div :id="getPlayerId(monitor)" 
                 class="monitor-player"
                 :style="{ minHeight: '200px', backgroundColor: '#1a1a1a' }"></div>
          </div>
          
          <!-- 离线监控显示 -->
          <div v-else class="offline-monitor">
            <div class="offline-icon">📷</div>
            <div class="offline-text">设备{{ getStatusText(monitor.channelStatus) }}</div>
          </div>
        </div>
        
        <!-- 空监控位置 -->
        <div v-else class="monitor-bg empty-monitor">
          <div class="monitor-name" title="等待监控">等待监控</div>
          <div class="empty-monitor-placeholder">
            <div class="placeholder-icon">📷</div>
          </div>
        </div>
      </div>
    </div>
    
    <div v-if="monitorData.length === 0 && !loading" class="empty-state">
      <div class="empty-text">暂无监控设备</div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted, onBeforeUnmount, onActivated } from 'vue';
import playerManager from '@/utils/playerManagers.js';

const props = defineProps({
  monitorData: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  itemsPerRow: {
    type: Number,
    default: 4
  }
});

// 移除 fullscreen 事件的 emit 定义
// const emit = defineEmits(['fullscreen']);

const playerLoadingStates = ref(new Map());
const playerErrorStates = ref(new Map());
const observer = ref(null);
const monitorVideoGrid = ref(null);

const displayedMonitorRows = computed(() => {
  const rows = [];
  const itemsPerRow = props.itemsPerRow;
  const monitors = props.monitorData;
  if (!monitors || monitors.length === 0) {
    return [];
  }
  for (let i = 0; i < monitors.length; i += itemsPerRow) {
    const row = monitors.slice(i, i + itemsPerRow);
    rows.push(row);
  }
  const lastRow = rows[rows.length - 1];
  if (lastRow && lastRow.length < itemsPerRow) {
    const placeholdersToAdd = itemsPerRow - lastRow.length;
    for (let i = 0; i < placeholdersToAdd; i++) {
      lastRow.push({
        id: `empty-monitor-${rows.length - 1}-${lastRow.length}`,
        isEmpty: true,
      });
    }
  }
  return rows;
});
const retryPlayer = async (monitor) => {
  const playerId = getPlayerId(monitor);
  console.log(`🔄 手动重试播放器: ${playerId}`);
  
  // 清除错误状态
  playerErrorStates.value.delete(playerId);
  
  // 重新初始化播放器
  await initializeMonitorPlayer(monitor);
};
function getPlayerId(monitor) {
  return `monitor-player-${monitor.deviceSerial}-${monitor.channelNo}`;
}

function getStatusText(status) {
  switch (status) {
    case '1': return '在线';
    case '0': return '离线';
    case '-1': return '异常';
    default: return '未知';
  }
}

// 3. 改进的观察者设置，添加更好的错误处理
const setupMonitorPlayerObserver = () => {
  console.log('🔍 设置监控播放器观察者...');
  
  if (observer.value) observer.value.disconnect();
  
  const options = {
    root: monitorVideoGrid.value,
    rootMargin: '100px 0px 200px 0px', // 增加预加载范围
    threshold: 0.2, // 降低阈值，更早触发
  };
  
  observer.value = new IntersectionObserver((entries) => {
    // 使用防抖和批处理
    const visibleMonitors = [];
    
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        const target = entry.target;
        const monitorId = target.dataset.monitorId;
        if (!monitorId) return;
        
        const monitor = props.monitorData.find(
          (m) => !m.isEmpty && `${m.deviceSerial}-${m.channelNo}` === monitorId
        );
        
        if (monitor && monitor.channelStatus === '1') {
          const playerId = getPlayerId(monitor);
          if (!playerManager.hasPlayer(playerId) && !playerLoadingStates.value.has(playerId)) {
            visibleMonitors.push(monitor);
          }
        }
        observer.value.unobserve(target);
      }
    });
    
    // 批量初始化，间隔处理
    if (visibleMonitors.length > 0) {
      console.log(`👁️ 发现 ${visibleMonitors.length} 个可见监控，开始批量初始化`);
      initializeVisibleMonitors(visibleMonitors);
    }
  }, options);

  if (monitorVideoGrid.value) {
    const elementsToObserve = monitorVideoGrid.value.querySelectorAll('.video-item[data-monitor-id]');
    elementsToObserve.forEach((el) => {
      observer.value.observe(el);
    });
    console.log(`✅ 已设置 ${elementsToObserve.length} 个监控的观察者`);
  }
};

const initializeMonitorPlayer = async (monitor) => {
  const playerId = getPlayerId(monitor);
  await playerManager.initPlayer({
    data: monitor,
    type: 'monitor',
    onLoadingChange: (playerId, isLoading) => {
      console.log('onLoadingChange', playerId, isLoading);
      if (isLoading) {
        playerLoadingStates.value.set(playerId, true);
      } else {
        playerLoadingStates.value.delete(playerId);
      }
    },
    onError: (playerId, error) => {
      playerErrorStates.value.set(playerId, error.message);
      playerLoadingStates.value.delete(playerId);
    }
  });
};

// 4. 批量初始化可见监控，避免同时初始化
const initializeVisibleMonitors = async (monitors) => {
  for (let i = 0; i < monitors.length; i++) {
    const monitor = monitors[i];
    console.log(`🎬 批量初始化监控 ${i + 1}/${monitors.length}: ${monitor.storeName}`);
    
    try {
      await initializeMonitorPlayer(monitor);
      
      // 每个播放器之间间隔，避免资源竞争
      if (i < monitors.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 800));
      }
    } catch (error) {
      console.error(`❌ 批量初始化监控失败: ${monitor.storeName}`, error);
    }
  }
  console.log('✅ 批量初始化完成');
};

// 5. 改进的reinitializePlayers方法
const reinitializePlayers = async () => {
  console.log('🔄 MonitorVideo: 重新初始化播放器...');
  
  // 清理现有状态
  playerLoadingStates.value.clear();
  playerErrorStates.value.clear();
  
  // 等待DOM稳定
  await new Promise(resolve => setTimeout(resolve, 500));
  await nextTick();
  
  // 重新设置观察者
  setupMonitorPlayerObserver();
  
  console.log('✅ MonitorVideo: 播放器重新初始化完成');
};

// 使用onActivated钩子处理keep-alive组件激活
onActivated(() => {
  console.log('MonitorVideo组件激活');
  reinitializePlayers();
});

onMounted(() => {
  nextTick(() => {
    setupMonitorPlayerObserver();
  });
});

onBeforeUnmount(() => {
  if (observer.value) observer.value.disconnect();
  // 注意：这里不销毁播放器，让父组件控制生命周期
  // playerManager.destroyAllPlayers('monitor');
});

watch(() => props.monitorData, async () => {
  await nextTick();
  setupMonitorPlayerObserver();
}, { deep: true });

// 暴露方法给父组件
defineExpose({
  reinitializePlayers
});
</script>

<style lang="scss" scoped>
.video-row {
  min-height: 120px;
  display: flex;
  gap: 15px;
  flex-shrink: 0;
}

.video-item {
  flex: 1;
  aspect-ratio: 16/9;
  min-height: 140px;
  border-radius: 8px;
  overflow: hidden;
}


.monitor-bg {
  width: 100%;
  height: 100%;
  display: block;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  padding: 0;
  transition: all 0.3s ease;
}

.monitor-bg:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 255, 255, 0.3);
}

/* 移除了可点击监控视频和全屏按钮的样式 */

.monitor-name {
  position: absolute;
  bottom: 20px;
  right: 0;
  z-index: 5;
  width: 50%;
  height: auto;
  padding: 8px 12px;
  box-sizing: border-box;
  // background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0) 100%);
  display: block;
  font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 24px;
  color: #00FFFF;
  text-align: right;
  font-style: normal;
  text-transform: none;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.monitor-player {
  width: 100%;
  height: 100%;
  background: #000;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.monitor-player-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

// 空状态样式
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: rgba(255, 255, 255, 0.6);
}

.error-text-small {
  color: #FF6B6B;
  font-size: 10px;
  font-weight: 500;
  margin-top: 4px;
  text-align: center;
}

.loading-spinner-small {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(0, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #00FFFF;
  animation: spin 1s ease-in-out infinite;
  margin-right: 10px;
}

.player-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: 4px;
}

.player-error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(40, 44, 52, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: 4px;
  border: 1px solid rgba(244, 67, 54, 0.3);
}

.loading-text-small {
  color: #00FFFF;
  font-size: 10px;
  font-weight: 500;
  margin-top: 4px;
  text-align: center;
}

.empty-text {
  font-size: 18px;
  font-weight: 500;
}

.error-text-small {
  color: #FF6B6B;
  font-size: 10px;
  font-weight: 500;
  margin-top: 4px;
  text-align: center;
}

.error-icon {
  font-size: 20px;
  opacity: 0.8;
  margin-bottom: 2px;
}

.offline-monitor {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2px dashed rgba(255, 255, 255, 0.3);
}

.offline-icon {
  font-size: 24px;
  opacity: 0.5;
  margin-bottom: 4px;
}

.offline-text {
  color: rgba(255, 255, 255, 0.6);
  font-size: 11px;
  text-align: center;
}

.empty-monitor {
  opacity: 0.3;
  pointer-events: none;
}

.empty-monitor-placeholder {
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed rgba(255, 255, 255, 0.2);
  flex-shrink: 0;
}

.placeholder-icon {
  font-size: 30px;
  opacity: 0.5;
}

.video-grid {
  width: 100%;
  height: calc(100% - 80px);
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 15px;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;

  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;

    &:hover {
      background: rgba(255, 255, 255, 0.5);
    }
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.monitor-player-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 200px;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  border-radius: 8px;
  overflow: hidden;
}

/* 🔥 关键改进：默认占位背景 */
.monitor-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
  transition: opacity 0.3s ease;
}

.monitor-placeholder.hidden {
  opacity: 0;
  pointer-events: none;
}

.placeholder-content {
  text-align: center;
  color: #ffffff;
}

.monitor-icon {
  font-size: 48px;
  margin-bottom: 12px;
  opacity: 0.7;
}

.monitor-title {
  font-size: 16px;
  font-weight: 500;
  opacity: 0.9;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 播放器容器优化 */
.monitor-player {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 200px;
  background: #1a1a1a; /* 深色背景，避免纯黑 */
  border-radius: 8px;
  overflow: hidden;
  z-index: 2;
}

/* 确保EZUIKit播放器填满容器 */
.monitor-player :deep(.ezuikit-video-player) {
  width: 100% !important;
  height: 100% !important;
  background: #1a1a1a !important;
}

.monitor-player :deep(video) {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover;
  background: #1a1a1a;
}

/* 加载状态优化 */
.player-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 3;
  color: #00FFFF;
}

.loading-spinner-small {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(0, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #00FFFF;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

.loading-text-small {
  font-size: 14px;
  color: #00FFFF;
  text-align: center;
}

/* 错误状态优化 */
.player-error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(244, 67, 54, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 3;
  color: #ff6b6b;
}

.error-icon {
  font-size: 36px;
  margin-bottom: 8px;
}

.error-text-small {
  font-size: 14px;
  margin-bottom: 12px;
  text-align: center;
}

.retry-btn-small {
  padding: 6px 12px;
  background: rgba(0, 255, 255, 0.2);
  border: 1px solid #00FFFF;
  border-radius: 4px;
  color: #00FFFF;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.retry-btn-small:hover {
  background: rgba(0, 255, 255, 0.3);
  transform: scale(1.05);
}

/* 离线状态优化 */
.offline-monitor {
  position: relative;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #424242 0%, #616161 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  color: #bdbdbd;
}

.offline-icon {
  font-size: 48px;
  margin-bottom: 12px;
  opacity: 0.7;
}

.offline-text {
  font-size: 16px;
  font-weight: 500;
}

/* 动画优化 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 视频网格容器优化 */
.video-grid {
  width: 100%;
  height: calc(100% - 80px);
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 15px;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
  /* 🔥 关键：确保容器有明确的尺寸 */
  min-height: 400px;
}

.video-row {
  min-height: 200px;
  display: flex;
  gap: 15px;
  flex-shrink: 0;
}

.video-item {
  flex: 1;
  aspect-ratio: 16/9;
  min-height: 200px;
  border-radius: 8px;
  overflow: hidden;
  /* 🔥 确保每个视频项都有最小尺寸 */
  min-width: 280px;
}

.monitor-bg {
  width: 100%;
  height: 100%;
  display: block;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  padding: 0;
  transition: all 0.3s ease;
  /* 🔥 避免纯黑背景 */
  background: #1a1a1a;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.monitor-bg:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 255, 255, 0.3);
}
</style>