<template>
  <div class="chart-container" ref="chartContainer"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue';
import * as echarts from 'echarts';

const props = defineProps({
  barData: {
    type: Object,
    required: true
  },
  xData: {
    type: Array,
    default: () => []
  },
  width: {
    type: String,
    default: '100%'
  },
  height: {
    type: String,
    default: '100%'
  },
  color: {
    type: String,
    default: "255,213,0",
  }
});

const chartContainer = ref(null);
let chart = null;

const initChart = () => {
  if (!chartContainer.value) return;
  
  if (!chart) {
    chart = echarts.init(chartContainer.value);
  }
  updateChart();
};

const updateChart = () => {
  if (!chart) return;
  
  console.log('LineChart updating with data:', {
    barData: props.barData,
    xData: props.xData
  });

  // 确保数据有效性
  const lineValues = props.barData?.data || [];
  const xAxisData = props.xData && props.xData.length > 0 ? props.xData : ['3月', '4月', '5月', '6月', '7月'];
  
  // 计算最大值，如果小于5则设为5
  const maxValue = Math.max(...lineValues, 0);
  const yAxisMax = maxValue < 5 ? 5 : undefined;
  
  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'line',
        label: {
          show: false
        },
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)',
          type: 'dashed'
        }
      },
      backgroundColor: 'rgba(0,0,0,0.3)',
      borderColor: 'rgba(255, 255, 255, 0.2)',
      textStyle: {
        color: '#fff',
        fontSize: 26
      },
      formatter: function(params) {
        const month = params[0].name;
        const value = params[0].value;
        
        // 检查是否有原始数据
        const rawData = props.barData?.rawData?.[month];
        
        if (rawData) {
          // 从原始数据中提取三个值
          const parts = rawData.split(':');
          const value1 = parts[0] || '0';
          const value2 = parts[1] || '0';
          const value3 = parts[2] || '0.00';
          
          const name1 = props.barData?.name || '问题个数';
          const name2 = props.barData?.secondName || '严重问题个数';
          const name3 = props.barData?.thirdName || '占比';
          
          return `${month}<br/>${name1}: ${value1}<br/>${name2}: ${value2}<br/>${name3}: ${value3}%`;
        } else {
          // 使用默认格式
          const name = props.barData?.name || '数量';
          return `${month}<br/>${name}: ${value}`;
        }
      }
    },
    grid: {
      top: '15%',
      left: '8%',
      right: '8%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.2)'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#beceff',
        fontSize: 26
      }
    },
    yAxis: {
      type: 'value',
      name: '数量',
      nameTextStyle: {
        color: 'rgba(255, 255, 255, 0.6)',
        fontSize: 24
      },
      min: 0,
      max: yAxisMax,
      // 强制使用整数间隔
      minInterval: 1,
      // 动态计算最大值
      scale: yAxisMax === undefined,
      splitNumber: 5,
      // 优化刻度线算法，使其更自然
      axisLabel: {
        color: 'rgba(170, 170, 170, 1)',
        fontSize: 26,
        formatter: function(value) {
          // 确保显示整数
          return parseInt(value);
        }
      },
      splitLine: {
        lineStyle: {
          color: '#4D5359'
        }
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      }
    },
    series: [
      // 折线图
      {
        name: props.barData?.name || '数量',
        type: 'line',
        data: lineValues.map(function (val, index) {
          return {
            value: val,
            label: {
              normal: {
                show: true, // 显示标签
                formatter: function () {
                  // 只显示数值
                  return `{a|${val}}`;
                },
                position: "top",
                rich: {
                  a: {
                    color: "#fff",
                    fontSize: 24,
                    fontWeight: "bold",
                    lineHeight: 28
                  }
                }
              },
            }
          };
        }),
        symbol: 'circle',
        symbolSize: 8,
        smooth: true,
        itemStyle: {
          color: `rgba(${props.color}, 1)`
        },
        lineStyle: {
          width: 3,
          color: `rgba(${props.color}, 1)`
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: `rgba(${props.color}, 0.6)` },
            { offset: 1, color: `rgba(${props.color}, 0.1)` }
          ])
        }
      }
    ]
  };
  
  chart.setOption(option, true);
};

watch(
  () => [props.barData, props.xData],
  () => {
    console.log('LineChart props changed, updating chart...');
    updateChart();
  },
  { deep: true, immediate: true }
);

onMounted(() => {
  initChart();
  window.addEventListener('resize', () => chart?.resize());
});

onUnmounted(() => {
  chart?.dispose();
  window.removeEventListener('resize', () => chart?.resize());
});
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 100%;
}
</style>