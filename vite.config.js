import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import { resolve } from "path";
import AutoImport from "unplugin-auto-import/vite";
import Components from "unplugin-vue-components/vite";
import { ElementPlusResolver } from "unplugin-vue-components/resolvers";
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'
import path from 'path'
import { viteZip } from 'vite-plugin-zip-file'

const useZip = process.env.VITE_USE_ZIP === 'true';

export default defineConfig({
  build: {
    minify: 'terser',
    sourcemap: false, // 不生成 source map（可减少体积）
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
        pure_funcs: ['console.log'], // 彻底去除 console.log
      },
      format: {
        comments: false, // 删除注释
      },
    },
    rollupOptions: {
      output: {
        manualChunks(id) {
          if (id.includes('three')) {
            return 'three';
          }
          if (id.includes('node_modules')) {
            return 'vendor';
          }
        },
      },
    },
    brotliSize: false, // 关闭 brotli 分析，加快构建速度
  },
  css: {
    minify: true,
    preprocessorOptions: {
      scss: {
        silenceDeprecations: ["legacy-js-api", "color-functions"],
      },
    },
  },
  plugins: [
    vue(),
    AutoImport({
      resolvers: [ElementPlusResolver()],
    }),
    Components({
      resolvers: [ElementPlusResolver()],
    }),
    createSvgIconsPlugin({
      iconDirs: [path.resolve(process.cwd(), 'src/assets/images/svg')],
      symbolId: 'icon-[name]'
    }),
    ...(useZip ? [viteZip({
      folderPath: path.resolve(__dirname, 'dist'),
      outPath: path.resolve(__dirname),
      withoutMainFolder: true,
      zipName: (() => {
        const now = new Date();
        const pad = n => n.toString().padStart(2, '0');
        const y = now.getFullYear();
        const m = pad(now.getMonth() + 1);
        const d = pad(now.getDate());
        const h = pad(now.getHours());
        const min = pad(now.getMinutes());
        const s = pad(now.getSeconds());
        return `经营大屏-${y}${m}${d}-${h}${min}${s}.zip`;
      })()
    })] : [])
  ],
  resolve: {
    extensions: [".js", ".vue", ".json", ".scss", ".css"],
    alias: {
      "@": resolve(__dirname, "src"),
    },
  },
  base: "./",
  server: {
    host: "0.0.0.0",
    hmr: true,
    proxy: {
      '/admin-api': {
        target: 'http://*************:31822',
        changeOrigin: true,
        rewrite: (path) => path
      },
      '/oauth': {
        target: 'https://api2.hik-cloud.com',//海康云牟
        changeOrigin: true,
      },
      '/v1': {
        target: 'https://api2.hik-cloud.com',//海康云牟
        changeOrigin: true,
      },
    }
  },
});
