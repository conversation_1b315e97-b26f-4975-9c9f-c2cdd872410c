<template>
  <div class="business-country-container" :style="style">
    <!-- 简化的头部 -->
    <div class="simple-header">
      <!-- 左侧返回按钮 -->
      <div class="header-left">
        <div class="back-button" @click="back">
          <img src="@/assets/images/header/back.png" alt="返回" />
          <span>返回</span>
        </div>
      </div>
      
      <!-- 中间标题 -->
      <div class="header-center">
        <div class="title-wrapper">
          <img src="@/assets/images/header/ywbj.png" alt="背景" class="title-bg" />
          <div class="title-text">
            <div class="platform-title">{{ country }}</div>
          </div>
        </div>
      </div>
      
      <!-- 右上角控制区域 -->
      <div class="header-controls">
        <!-- 展开/收起控制 -->
        <div class="control-item" @click="toggleExpand">
          <span class="control-text">{{ isExpanded ? '收起' : '展开' }}</span>
        </div>
        
        <!-- 切换到现场视频的图标 -->
        <div class="control-item video-switch" @click="goToVideo" title="现场视频">
          <img src="@/assets/images/header/视频.png" alt="现场视频" class="video-icon" />
        </div>
      </div>
    </div>

    <!-- 国家详情内容 -->
    <div class="project-container absolute top-0 left-0 pt-[120px] px-[20px] box-border w-full h-full bg-no-repeat bg-cover flex justify-between"
         :class="{ 'expanded': isExpanded }">
      <div class="leftList" :class="{ 'leftList-china': isChina }">
        <div v-if="infoLists.length === 0" class="items" v-for="(item, index) in infoList" :key="index">
          <div class="item-img">
            <RotatingCircle style="width: 100px;height: 100px;"><svg-icon :name="item.svgName" color="#fff"
                style="width: 50px;height: 50px;" /></RotatingCircle>
          </div>
          <div class="item-content">
            <div class="item-title">{{ item.name }}</div>
            <div class="item-amount">
              <span class="text-[30px] font-extrabold text-[#EEF4FF] mr-2 bgfont">
                <count-to v-if="item.needCount" :start-val="0" :end-val="item.amount" :duration="2000" :decimals="2"
                  :autoplay="true" separator="," />
                <template v-else>{{ item.amount }}</template>
              </span><span class="bgfont">{{ item.unit }}</span>
            </div>
          </div>
          <div class="absolute right-3 top-20" v-if="item.id === 'renshu'">
            <template v-if="isChina">
              <div>
                <span class="bg-gradient">{{ t('country.formalEmployees') }}</span>
                <span class="text-[20px] font-extrabold text-[#EEF4FF] ml-5 mr-2 font-['xiaoweiLogo']">{{
                  zsyg }}</span><span>{{ t('country.units.people') }}</span>
              </div>
              <div>
                <span class="bg-gradient">{{ t('country.otherEmploymentForms') }}</span>
                <span class="text-[20px] font-extrabold text-[#EEF4FF] ml-5 mr-2 font-['xiaoweiLogo']">{{
                  (lwpq) + (qtyg) + (hzdw) }}</span><span>{{ t('country.units.people') }}</span>
              </div>
            </template>
            <template v-else>
              <div>
                <span class="bg-gradient">{{ t('country.chinese') }}</span>
                <span class="text-[20px] font-extrabold text-[#EEF4FF] ml-5 mr-2 font-['xiaoweiLogo']">{{
                  gnrs }}</span><span>{{ t('country.units.people') }}</span>
              </div>
              <div>
                <span class="bg-gradient">{{ t('country.foreign') }}</span>
                <span class="text-[20px] font-extrabold text-[#EEF4FF] ml-5 mr-2 font-['xiaoweiLogo']">{{
                  hwrs }}</span><span>{{ t('country.units.people') }}</span>
              </div>
            </template>
          </div>
        </div>
        <div v-if="infoLists.length > 0" class="items" v-for="(item, index) in infoLists" :key="index">
          <div class="item-img">
            <RotatingCircle style="width: 100px;height: 100px;"><svg-icon :name="item.svgName" color="#fff"
                style="width: 50px;height: 50px;" /></RotatingCircle>
          </div>
          <div class="item-content">
            <div class="item-title">{{ item.name }}</div>
            <div class="item-amount">
              <span class="text-[30px] font-extrabold text-[#EEF4FF] mr-2 bgfont">
                <count-to v-if="item.needCount" :start-val="0" :end-val="item.amount" :duration="2000" :decimals="2"
                  :autoplay="true" separator="," />
                <template v-else>{{ item.amount }}</template>
              </span><span class="bgfont">{{ item.unit }}</span>
            </div>
          </div>
          <div class="absolute right-3 top-20" v-if="item.id === 'renshu'">
            <template v-if="isChina">
              <div>
                <span class="bg-gradient">{{ t('country.formalEmployees') }}</span>
                <span class="text-[20px] font-extrabold text-[#EEF4FF] ml-5 mr-2 font-['xiaoweiLogo']">{{
                  zsyg }}</span><span>{{ t('country.units.people') }}</span>
              </div>
              <div>
                <span class="bg-gradient">{{ t('country.otherEmploymentForms') }}</span>
                <span class="text-[20px] font-extrabold text-[#EEF4FF] ml-5 mr-2 font-['xiaoweiLogo']">{{
                  (lwpq) + (qtyg) + (hzdw) }}</span><span>{{ t('country.units.people') }}</span>
              </div>
            </template>
            <template v-else>
              <div>
                <span class="bg-gradient">{{ t('country.chinese') }}</span>
                <span class="text-[20px] font-extrabold text-[#EEF4FF] ml-5 mr-2 font-['xiaoweiLogo']">{{
                  gnrs }}</span><span>{{ t('country.units.people') }}</span>
              </div>
              <div>
                <span class="bg-gradient">{{ t('country.foreign') }}</span>
                <span class="text-[20px] font-extrabold text-[#EEF4FF] ml-5 mr-2 font-['xiaoweiLogo']">{{
                  hwrs }}</span><span>{{ t('country.units.people') }}</span>
              </div>
            </template>
          </div>
        </div>
      </div>

      <div class="right-list-container" v-if="rightList.length > 0">
        <div class="list-content">
          <div
            v-for="(item, index) in rightList"
            :key="item.data.项目ID || item.data.营地id"
            class="list-item"
            @click="onRightListItemClick(item)"
            @mouseenter="handleItemHover(item)"
            @mouseleave="clearHighlight"
          >
            <span class="item-index">{{ index + 1 }}</span>
            <span class="item-name">{{ item.name }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 地球/地图显示 - 独立显示层 -->
    <div class="earth-map-container">
      <iframe src="./zjsj-yydp/indexDp.html" id="iframe" class="w-full h-full iframe-container"></iframe>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, onBeforeUnmount } from "vue";
import { useRouter, useRoute } from "vue-router";
import { useI18n } from "vue-i18n";
import { CountTo } from 'vue3-count-to';
import RotatingCircle from '@/components/RotatingCircle.vue';
import SvgIcon from '@/components/SvgIcon.vue';
import request from "@/utils/request";
import { useIndex } from '@/assets/js/windowScale.js';

const router = useRouter();
const route = useRoute();
const iframe = ref(null);
const { t, locale } = useI18n();

// WindowScale适配功能 - 响应式样式方式
const { style, setScale, getScale } = useIndex();

// 简化头部的状态
const isExpanded = ref(false);

const rightList = ref([]);
const isChina = ref(localStorage.getItem("isChina") == "1" ? true : false);
const country = ref(sessionStorage.getItem("clickCountry") || "中国");
const countryData = ref(0);
const projectApiData = ref([]);
const campApiData = ref([]);
const gcksr = ref(0);
const htze = ref(0);
const bnmb = ref(0);
const renshu = ref(0);
const gnrs = ref(0);
const hwrs = ref(0);
const zsyg = ref(0);
const lwpq = ref(0);
const qtyg = ref(0);
const hzdw = ref(0);
const pieData = ref([]);
const lastBackTime = ref(0);
let throttleTimer = ref(null);
const isLoading = ref(true);

// 展开/收起功能
const toggleExpand = () => {
  if (!iframe.value) {
    iframe.value = document.getElementById("iframe");
    if (!iframe.value) return;
  }

  if (isExpanded.value === false) {
    isExpanded.value = true;
    iframe.value.contentWindow.postMessage(
      { eve: "changeModel", data: "plan" },
      "*"
    );
  } else {
    isExpanded.value = false;
    iframe.value.contentWindow.postMessage(
      { eve: "changeModel", data: "earth" },
      "*"
    );
  }
};

// 跳转到现场视频页面
const goToVideo = () => {
  router.push('/video-display');
};

// 返回到业务布局主页
const back = () => {
  const now = Date.now();
  if (now - lastBackTime.value < 1000) return;
  lastBackTime.value = now;

  if (throttleTimer.value) {
    clearTimeout(throttleTimer.value);
  }

  throttleTimer.value = setTimeout(() => {
    router.push('/business-display');
  }, 100);
};

const infoList = computed(() => [
  { id: "projects", name: t('country.projectsUnderConstruction'), amount: 0, unit: t('country.units.projects'), svgName: "home-2", needCount: false },
  { id: "htze", name: t('country.contractAmount'), amount: 0, unit: "", svgName: "", needCount: true },
  { id: "bnmb", name: t('country.cumulativeOutput'), amount: 0, unit: "", svgName: "", needCount: true },
  { id: "gcksr", name: t('country.engineeringRevenue'), amount: 0, unit: "", svgName: "", needCount: true },
  { id: "renshu", name: t('country.overseasPersonnel'), amount: 0, unit: t('country.units.people'), svgName: "home-6", needCount: false }
]);

const infoLists = ref([]);

// 安全数字转换函数，防止NaN
function safeNumber(val) {
  const n = Number(val);
  return isNaN(n) ? 0 : n;
}

// 消息处理函数
const handleMessage = (e) => {
  console.log(e.data, '这是business-country');

  if (e.data.eve === 'initDemoInfo' && e.data.title) {
    const level = e.data.level || 'district';
    getInfoByLevel(level, e.data.title);
    return;
  }
  if (e.data.eve === 'cancle') {
    rightList.value = [];
  }

  if (e.data.type === "project") {
    console.log(e.data, '项目');
    rightList.value = e.data.data.map(item => ({
      name: item.项目名称,
      data: item
    }));
  }

  if (e.data.type === "camp") {
    console.log(e.data, '营地');
    rightList.value = e.data.data.map(item => ({
      name: item.营地名称,
      data: item
    }));
  }
};

const getInfoByLevel = async (level, name) => {
  try {
    console.log(`获取${level}数据:`, name);
    const response = await request.get('/globalManage/zjmanage/largescreen/getInfoByLevel', {
      params: { level, name }
    });
    
    if (response.code === 0 && response.data.length > 0) {
      const data = response.data[0];
      console.log('获取到的数据:', data);
      
      // 更新数据
      htze.value = safeNumber(data.合同总额);
      bnmb.value = safeNumber(data.本年目标);
      gcksr.value = safeNumber(data.工程款收入);
      renshu.value = safeNumber(data.项目总人数);
      gnrs.value = safeNumber(data.中方);
      hwrs.value = safeNumber(data.外籍);
      zsyg.value = safeNumber(data.正式员工);
      lwpq.value = safeNumber(data.劳务派遣);
      qtyg.value = safeNumber(data.其他用工);
      hzdw.value = safeNumber(data.合作单位);
      
      // 构建信息列表
      infoLists.value = [
        { 
          id: "projects", 
          name: t('country.projectsUnderConstruction'), 
          amount: safeNumber(data.在建项目数量), 
          unit: t('country.units.projects'), 
          svgName: "home-2", 
          needCount: false 
        },
        { 
          id: "htze", 
          name: t('country.contractAmount'), 
          amount: htze.value, 
          unit: isChina.value ? t('country.units.hundredMillionYuan') : t('country.units.hundredMillionUSD'), 
          svgName: "home-3-l", 
          needCount: true 
        },
        { 
          id: "bnmb", 
          name: t('country.cumulativeOutput'), 
          amount: bnmb.value, 
          unit: isChina.value ? t('country.units.hundredMillionYuan') : t('country.units.hundredMillionUSD'), 
          svgName: "home-4-l", 
          needCount: true 
        },
        { 
          id: "gcksr", 
          name: t('country.engineeringRevenue'), 
          amount: gcksr.value, 
          unit: isChina.value ? t('country.units.hundredMillionYuan') : t('country.units.hundredMillionUSD'), 
          svgName: "home-5-l", 
          needCount: true 
        },
        { 
          id: "renshu", 
          name: t('country.overseasPersonnel'), 
          amount: renshu.value, 
          unit: t('country.units.people'), 
          svgName: "home-6", 
          needCount: false 
        }
      ];
    }
  } catch (error) {
    console.error('获取层级数据失败:', error);
  }
};

const onRightListItemClick = (item) => {
  console.log('点击项目/营地:', item);
  
  // 存储点击的项目信息
  if (item.data.项目ID) {
    // 项目数据
    localStorage.setItem("clickProject", item.name);
    localStorage.setItem("projectData", JSON.stringify(item.data));
    
    // 跳转到项目详情页面
    router.push('/business-display/project-detail');
  } else if (item.data.营地id) {
    // 营地数据 - 暂时也跳转到项目详情（可以后续扩展营地详情页面）
    localStorage.setItem("clickProject", item.name);
    localStorage.setItem("projectData", JSON.stringify(item.data));
    
    // 跳转到项目详情页面
    router.push('/business-display/project-detail');
  }
};

const handleItemHover = (item) => {
  console.log('悬停项目/营地:', item);
  // 可以添加高亮显示逻辑
};

const clearHighlight = () => {
  // 清除高亮显示
};

onMounted(async () => {
  // 初始化iframe
  iframe.value = document.getElementById("iframe");
  
  // 添加消息监听
  window.addEventListener("message", handleMessage);
  
  // 向iframe发送token等初始化消息
  if (iframe.value) {
    setTimeout(() => {
      iframe.value.contentWindow.postMessage(
        {
          type: "token",
          data: sessionStorage.getItem('token'),
        },
        "*"
      );
      console.log('发送token到business-country iframe');
    }, 2000);
  }
  
  isLoading.value = false;
});

onBeforeUnmount(() => {
  window.removeEventListener("message", handleMessage);
  if (throttleTimer.value) {
    clearTimeout(throttleTimer.value);
  }
});
</script>

<style lang="scss" scoped>
.business-country-container {
  background: #0a0f1a;
  transform-origin: center center;
  position: absolute;
  top: 50%;
  left: 50%;
  overflow: hidden;
  // 具体的width、height、transform由响应式style对象控制
}

.simple-header {
  width: 100%;
  height: 122px;
  position: fixed;
  top: 0px;
  left: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-left {
  position: absolute;
  top: 19px;
  left: 20px;
  z-index: 101;
}

.back-button {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 4px;
  transition: all 0.3s ease;
  pointer-events: auto;
  gap: 8px;

  img {
    width: 24px;
    height: 24px;
  }

  span {
    color: white;
    font-size: 18px;
    font-family: Alibaba PuHuiTi 2.0, sans-serif;
    font-weight: normal;
  }

  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }
}

.header-center {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.title-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.title-bg {
  height: 120px;
}

.title-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -60%);
  width: 100%;
  text-align: center;
}

.platform-title {
  font-size: 32px;
  color: #EFF8FC;
  font-family: YouSheBiaoTiHei, sans-serif;
  font-weight: 400;
  letter-spacing: 2px;
  text-shadow: inset 0px 0px 1px rgba(255, 255, 255, 0.8), 
               0px 0px 7px rgba(130, 165, 255, 0.54), 
               0px 2px 0px rgba(19, 80, 143, 0.66);
  white-space: nowrap;
}

.header-controls {
  position: absolute;
  top: 19px;
  right: 20px;
  display: flex;
  align-items: center;
  gap: 20px;
  z-index: 101;
}

.control-item {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 4px;
  transition: all 0.3s ease;
  pointer-events: auto;
}

.control-text {
  color: white;
  font-size: 18px;
  font-family: Alibaba PuHuiTi 2.0, sans-serif;
  font-weight: normal;
}

.video-switch {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 40px;
}

.video-icon {
  width: 50px;
  height: 30px;
}

/* 项目页面的原始样式 */
.project-container {
  pointer-events: none;
  background: transparent;
  z-index: 3;
  transition: all 0.5s ease;
  
  &.expanded {
    transform: scale(1.05);
    filter: brightness(1.1);
  }

  .leftList {
    display: flex;
    flex-direction: column;
    position: relative;
    justify-content: space-around;
    height: 80%;
    top: 40px;
    z-index: 99;
    margin-left: 240px;
    pointer-events: auto;

    &.leftList-china {
      margin-left: 120px;
    }

    .item-img {
      position: relative;
      top: -16px;
    }

    .items {
      display: flex;
      gap: 20px;
      position: relative;

      .item-title {
        font-size: 30px;
      }

      .item-amount {
        font-family: "xiaoweiLogo";
      }
    }
  }

  .right-list-container {
    position: fixed;
    top: 120px;
    right: 20px;
    width: 300px;
    height: calc(100vh - 140px);
    background: rgba(0, 20, 40, 0.8);
    border: 1px solid rgba(100, 150, 255, 0.3);
    border-radius: 8px;
    backdrop-filter: blur(10px);
    pointer-events: auto;
    z-index: 99;

    .list-content {
      height: 100%;
      overflow-y: auto;
      padding: 10px;

      .list-item {
        display: flex;
        align-items: center;
        padding: 12px;
        margin-bottom: 8px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 1px solid transparent;

        &:hover {
          background: rgba(100, 150, 255, 0.2);
          border-color: rgba(100, 150, 255, 0.5);
          transform: translateX(5px);
        }

        .item-index {
          width: 30px;
          height: 24px;
          background: linear-gradient(135deg, #4A90E2, #67B3F3);
          color: white;
          font-size: 14px;
          font-weight: bold;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 4px;
          margin-right: 12px;
          flex-shrink: 0;
        }

        .item-name {
          color: #EFF8FC;
          font-size: 16px;
          font-weight: 500;
          line-height: 1.4;
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
}

.earth-map-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: all;
  background: url("@/assets/images/xiangmu/wangge.png") no-repeat center center / 100% 100%;
  
  .iframe-container {
    width: 100%;
    height: 100%;
    border: none;
  }
}

.bgfont {
  font-family: TCloudNumber;
  background-image: linear-gradient(to bottom,
      #9AC4FF 0%,
      #FFFFFF 50%,
      #9AC4FF 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
}

.bg-gradient {
  background: linear-gradient(180deg, rgba(0,132,255,0) 0%, #0084FF 50%, rgba(0,132,255,0) 100%);
  border-radius: 0px 0px 0px 0px;
  font-size: 18px;
  margin-right: 6px;
  padding: 2px 8px;
  color: #fff;
}
</style> 