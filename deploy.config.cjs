module.exports = {
  projectName: '经营大屏', // 项目名称
  dev: {  // 开发环境
    name: '开发环境',
    script: 'npm run build', // 打包命令
    host: '*************', // 服务器地址
    port: 22, // ssh port，一般默认22
    username: 'zjgj', // 登录服务器用户名
    password: process.env.DEPLOY_DEV_PASSWORD, // 从环境变量读取密码
    distPath: 'dist',  // 本地打包目录
    webDir: '/home/<USER>/jingying-daping',  // 服务器部署路径
    bakDir: '/home/<USER>/jingying-daping_back', // 备份路径
    isRemoveRemoteFile: false, // 是否删除远程文件
    isRemoveLocalFile: false // 是否删除本地文件
  },
  prod: {  
    
  }
} 