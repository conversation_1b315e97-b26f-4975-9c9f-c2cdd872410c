import { createRouter, createWebHashHistory } from "vue-router";

const routes = [
  { path: "/", redirect: { path: "/login" } },
  {
    path: "/login",
    name: "Login",
    component: () => import("../views/login/index.vue"),
  },
  // 业务布局展示页面路由组 - 独立全屏页面，不使用Layout包装，包含国家详情和项目详情子路由
  // 禁用keep-alive缓存以避免路由跳转问题
  {
    path: "/business-display",
    name: "businessDisplay",
    component: () => import("../views/business-display/index.vue"),
    meta: { keepAlive: false },
    children: [
      {
        path: "country",
        name: "businessDisplayCountry",
        component: () => import("../views/business-display/country.vue"),
        meta: { keepAlive: false, hideParent: true }
      },
      {
        path: "project-detail",
        name: "businessDisplayProjectDetail",
        component: () => import("../views/business-display/projectDetail.vue"),
        meta: { keepAlive: false, hideParent: true }
      },
      {
        path: "yingdi",
        name: "businessDisplayYingdi",
        component: () => import("../views/business-display/yingdi.vue"),
        meta: { keepAlive: false, hideParent: true }
      },
      {
        path: "video-display",
        name: "businessDisplayVideoDisplay",
        component: () => import("../views/video-display/index.vue"),
        meta: { keepAlive: false, hideParent: true }
      }
    ]
  },
  // 仅用于自动登录检测的路由
  {
    path: "/noAuth",
    name: "noAuth",
    component: () => import("../views/login/index.vue"), // 临时组件，实际会在路由守卫中处理
  },
  // 通配路由，将所有未匹配的路由重定向到登录页
  {
    path: "/:pathMatch(.*)*",
    redirect: "/login"
  }
];

const router = createRouter({
  history: createWebHashHistory(),
  routes,
});

export default router;
