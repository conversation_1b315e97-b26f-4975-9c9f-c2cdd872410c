<template>
  <div class="BarChart" ref="barChartRef"></div>
</template>

<script setup>
import { nextTick, ref, watch, markRaw, onMounted } from "vue";
import {
  getImg,
  numberToThousands,
  getPercent,
  formatNumber,
} from "@/utils/method";
import * as echarts from "echarts";

const props = defineProps({
  color: {
    type: String,
    default: "0, 255, 136",
  },
  data: {
    type: Array,
    default: () => [],
  },
  topColor: {
    type: String,
    default: "104,226,158",
  },
  chartData: {
    type: Object,
    default: () => ({ name: "", data: [], percentages: [], months: [] }),
  }
});

const barChartRef = ref(null);
const barChart = ref(null);

// 默认数据
let xData = ["3月", "4月", "5月", "6月", "7月"];
let yData = [0, 0, 0, 0, 0];
let percentages = ["0.00", "0.00", "0.00", "0.00", "0.00"];

onMounted(() => {
  initChart();
});

watch(
  () => props.chartData,
  (newData) => {
    console.log('BarChart received new chartData:', newData);
    if (newData && newData.data) {
      updateChartData(newData);
      initChart();
    }
  },
  { deep: true, immediate: true }
);

watch(
  () => props.data,
  (newData) => {
    if (newData && newData.length > 0) {
      yData = [...newData];
      initChart();
    }
  },
  { deep: true }
);

function updateChartData(chartData) {
  if (chartData.data && chartData.data.length > 0) {
    yData = [...chartData.data];
  }
  if (chartData.percentages && chartData.percentages.length > 0) {
    percentages = [...chartData.percentages];
  }
  if (chartData.months && chartData.months.length > 0) {
    xData = [...chartData.months];
  }
  
  console.log('Updated chart data:', {
    xData: xData,
    yData: yData,
    percentages: percentages
  });
}

function initChart() {
  if (!barChartRef.value) return;

  if (!barChart.value) {
    barChart.value = markRaw(echarts.init(barChartRef.value));
  } else {
    barChart.value.clear();
  }

  // 确保数据长度一致
  const dataLength = Math.max(xData.length, yData.length, percentages.length);
  
  // 补齐数据到相同长度
  while (xData.length < dataLength) xData.push(`${xData.length + 3}月`);
  while (yData.length < dataLength) yData.push(0);
  while (percentages.length < dataLength) percentages.push("0.00");

  // 计算最大值作为背景条的高度
  const maxValue = Math.max(...yData, 10); // 确保最小为10，避免空数据情况
  const bgValues = new Array(dataLength).fill(maxValue);

  let option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
        label: {
          show: false
        },
        shadowStyle: {
          color: 'rgba(255, 255, 255, 0.1)'
        }
      },
      backgroundColor: 'rgba(0,0,0,0.3)',
      borderColor: 'rgba(255, 255, 255, 0.2)',
      textStyle: {
        color: '#fff',
        fontSize: 26
      },
      formatter: function(params) {
        const dataIndex = params[0].dataIndex;
        
        // 从原始数据中获取三个值
        const monthData = props.chartData ? props.chartData.months[dataIndex] : xData[dataIndex];
        const rawData = props.chartData && props.chartData.rawData ? props.chartData.rawData[monthData] : '';
        
        if (rawData) {
          // 如果有原始数据，则直接使用
          const parts = rawData.split(':');
          const value1 = parts[0] || '0';
          const value2 = parts[1] || '0';
          const value3 = parts[2] || '0.00';
          
          const name1 = props.chartData.name || '隐患个数';
          const name2 = props.chartData.secondName || '严重隐患个数';
          const name3 = props.chartData.thirdName || '占比';
          
          return `${xData[dataIndex]}<br/>${name1}: ${value1}<br/>${name2}: ${value2}<br/>${name3}: ${value3}%`;
        } else {
          // 如果没有原始数据，则使用计算后的数据
          const value = yData[dataIndex];
          const percentage = percentages[dataIndex];
          const name = props.chartData.name || '隐患个数';
          const name2 = props.chartData.secondName || '严重隐患个数';
          const name3 = props.chartData.thirdName || '占比';
          
          return `${xData[dataIndex]}<br/>${name}: ${value}<br/>${name3}: ${percentage}%`;
        }
      }
    },
    grid: {
      top: "15%",
      left: "8%", 
      right: "8%",
      bottom: "15%",
      containLabel: true
    },
    xAxis: {
      data: xData,
      axisTick: { show: false },
      axisLine: { 
        show: true,
        lineStyle: {
          color: "#0c3b71",
        }
      },
      axisLabel: {
        interval: 0,
        textStyle: { color: "#beceff", fontSize: 26 },
      },
    },
    yAxis: [
      {
        type: "value",
        gridIndex: 0,
        min: 0,
        splitLine: {
          show: true,
          lineStyle: {
            color: "#4D5359",
          },
        },
        axisTick: {
          show: false,
        },
        axisLine: {
          lineStyle: {
            color: "#0c3b71",
          },
        },
        axisLabel: {
          color: "rgb(170,170,170)",
          formatter: "{value}",
          fontSize: 26
        },
      },
    ],
    series: [
      // 内部柱顶部（pictorialBar）
      {
        name: "",
        type: "pictorialBar",
        symbolSize: [12, 8],
        symbolOffset: [0, -5],
        z: 12,
        barGap: "-100%",
        data: yData.map(function (val) {
          return {
            value: val,
            symbolPosition: "end",
            itemStyle: {
              normal: {
                color: "rgba(28, 197, 227, 1)",
              },
            },
          };
        }),
      },
      // 内部柱底部（pictorialBar）
      {
        name: "",
        type: "pictorialBar",
        symbolSize: [12, 10],
        symbolOffset: [0, 5],
        z: 12,
        barGap: "-100%",
        data: yData.map(function (val) {
          return {
            value: val,
            itemStyle: {
              normal: {
                color: "rgba(28, 197, 227, 1)",
              },
            },
          };
        }),
      },
      // 内部柱主体（bar）
      {
        type: "bar",
        barWidth: 12,
        z: 10,
        barGap: "-100%",
        data: yData.map(function (val, index) {
          return {
            value: val,
            label: {
              normal: {
                show: true, // 显示标签
                formatter: function () {
                  // 显示数值和百分比
                  const percentage = percentages[index];
                  return `{a|${val}}\n{b|${percentage}%}`;
                },
                position: "top",
                rich: {
                  a: {
                    color: "#fff",
                    fontSize: 24,
                    fontWeight: "bold",
                    lineHeight: 28
                  },
                  b: {
                    color: "#8EE3F1",
                    fontSize: 22,
                    lineHeight: 26
                  }
                }
              },
            },
            itemStyle: {
              normal: {
                color: {
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  type: "linear",
                  global: false,
                  colorStops: [
                    { offset: 0, color: "#1CC5E3" },
                    { offset: 1, color: "#1CC5E3" },
                  ],
                },
              },
            },
          };
        }),
      },
      // 内部外圆圈（pictorialBar）
      {
        name: "",
        type: "pictorialBar",
        symbolSize: [18, 12],
        symbolOffset: [0, 8],
        z: 11,
        barGap: "-100%",
        data: bgValues.map(function (val) {
          return {
            value: val,
            itemStyle: {
              normal: {
                color: "rgba(28, 197, 227, 0.6)",
              },
            },
          };
        }),
      },
      // 最底层渐变条（bar）
      {
        type: "bar",
        barWidth: 12,
        barGap: "-100%",
        z: -1,
        data: bgValues.map(function (val) {
          return {
            value: val,
            label: { normal: { show: false } },
            itemStyle: {
              normal: {
                color: {
                  x: 1,
                  y: 1,
                  x2: 1,
                  y2: 0,
                  type: "linear",
                  global: false,
                  colorStops: [
                    { offset: 0, color: "rgba(28, 197, 227, 0.2)" },
                    { offset: 0.5, color: "rgba(28, 197, 227, 0.15)" },
                    { offset: 0.9, color: "rgba(28, 197, 227, 0.1)" },
                    { offset: 1, color: "rgba(28, 197, 227, 0)" },
                  ],
                },
              },
            },
          };
        }),
      },
    ],
  };
  
  barChart.value.setOption(option);
}
</script>

<style lang="scss" scoped>
.BarChart {
  width: 100%;
  height: 100%;
}
</style>