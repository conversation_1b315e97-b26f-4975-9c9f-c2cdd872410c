// vite.config.js
import { defineConfig } from "file:///C:/Users/<USER>/Desktop/zjsj-front-global-manage-1%20-%20%E5%89%AF%E6%9C%AC/node_modules/vite/dist/node/index.js";
import vue from "file:///C:/Users/<USER>/Desktop/zjsj-front-global-manage-1%20-%20%E5%89%AF%E6%9C%AC/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import { resolve } from "path";
import AutoImport from "file:///C:/Users/<USER>/Desktop/zjsj-front-global-manage-1%20-%20%E5%89%AF%E6%9C%AC/node_modules/unplugin-auto-import/dist/vite.js";
import Components from "file:///C:/Users/<USER>/Desktop/zjsj-front-global-manage-1%20-%20%E5%89%AF%E6%9C%AC/node_modules/unplugin-vue-components/dist/vite.mjs";
import { ElementPlusResolver } from "file:///C:/Users/<USER>/Desktop/zjsj-front-global-manage-1%20-%20%E5%89%AF%E6%9C%AC/node_modules/unplugin-vue-components/dist/resolvers.mjs";
import { createSvgIconsPlugin } from "file:///C:/Users/<USER>/Desktop/zjsj-front-global-manage-1%20-%20%E5%89%AF%E6%9C%AC/node_modules/vite-plugin-svg-icons/dist/index.mjs";
import path from "path";
import { viteZip } from "file:///C:/Users/<USER>/Desktop/zjsj-front-global-manage-1%20-%20%E5%89%AF%E6%9C%AC/node_modules/vite-plugin-zip-file/lib/index.mjs";
var __vite_injected_original_dirname = "C:\\Users\\<USER>\\Desktop\\zjsj-front-global-manage-1 - \u526F\u672C";
var useZip = process.env.VITE_USE_ZIP === "true";
var vite_config_default = defineConfig({
  build: {
    minify: "terser",
    sourcemap: false,
    // 不生成 source map（可减少体积）
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
        pure_funcs: ["console.log"]
        // 彻底去除 console.log
      },
      format: {
        comments: false
        // 删除注释
      }
    },
    rollupOptions: {
      output: {
        manualChunks(id) {
          if (id.includes("three")) {
            return "three";
          }
          if (id.includes("node_modules")) {
            return "vendor";
          }
        }
      }
    },
    brotliSize: false
    // 关闭 brotli 分析，加快构建速度
  },
  css: {
    minify: true,
    preprocessorOptions: {
      scss: {
        silenceDeprecations: ["legacy-js-api", "color-functions"]
      }
    }
  },
  plugins: [
    vue(),
    AutoImport({
      resolvers: [ElementPlusResolver()]
    }),
    Components({
      resolvers: [ElementPlusResolver()]
    }),
    createSvgIconsPlugin({
      iconDirs: [path.resolve(process.cwd(), "src/assets/images/svg")],
      symbolId: "icon-[name]"
    }),
    ...useZip ? [viteZip({
      folderPath: path.resolve(__vite_injected_original_dirname, "dist"),
      outPath: path.resolve(__vite_injected_original_dirname),
      withoutMainFolder: true,
      zipName: (() => {
        const now = /* @__PURE__ */ new Date();
        const pad = (n) => n.toString().padStart(2, "0");
        const y = now.getFullYear();
        const m = pad(now.getMonth() + 1);
        const d = pad(now.getDate());
        const h = pad(now.getHours());
        const min = pad(now.getMinutes());
        const s = pad(now.getSeconds());
        return `\u7ECF\u8425\u5927\u5C4F-${y}${m}${d}-${h}${min}${s}.zip`;
      })()
    })] : []
  ],
  resolve: {
    extensions: [".js", ".vue", ".json", ".scss", ".css"],
    alias: {
      "@": resolve(__vite_injected_original_dirname, "src")
    }
  },
  base: "./",
  server: {
    host: "0.0.0.0",
    hmr: true,
    proxy: {
      "/admin-api": {
        target: "http://*************:31822",
        changeOrigin: true,
        rewrite: (path2) => path2
      },
      "/oauth": {
        target: "https://api2.hik-cloud.com",
        //海康云牟
        changeOrigin: true
      },
      "/v1": {
        target: "https://api2.hik-cloud.com",
        //海康云牟
        changeOrigin: true
      }
    }
  }
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,ewogICJ2ZXJzaW9uIjogMywKICAic291cmNlcyI6IFsidml0ZS5jb25maWcuanMiXSwKICAic291cmNlc0NvbnRlbnQiOiBbImNvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9kaXJuYW1lID0gXCJDOlxcXFxVc2Vyc1xcXFx6elxcXFxEZXNrdG9wXFxcXHpqc2otZnJvbnQtZ2xvYmFsLW1hbmFnZS0xIC0gXHU1MjZGXHU2NzJDXCI7Y29uc3QgX192aXRlX2luamVjdGVkX29yaWdpbmFsX2ZpbGVuYW1lID0gXCJDOlxcXFxVc2Vyc1xcXFx6elxcXFxEZXNrdG9wXFxcXHpqc2otZnJvbnQtZ2xvYmFsLW1hbmFnZS0xIC0gXHU1MjZGXHU2NzJDXFxcXHZpdGUuY29uZmlnLmpzXCI7Y29uc3QgX192aXRlX2luamVjdGVkX29yaWdpbmFsX2ltcG9ydF9tZXRhX3VybCA9IFwiZmlsZTovLy9DOi9Vc2Vycy96ei9EZXNrdG9wL3pqc2otZnJvbnQtZ2xvYmFsLW1hbmFnZS0xJTIwLSUyMCVFNSU4OSVBRiVFNiU5QyVBQy92aXRlLmNvbmZpZy5qc1wiO2ltcG9ydCB7IGRlZmluZUNvbmZpZyB9IGZyb20gXCJ2aXRlXCI7XHJcbmltcG9ydCB2dWUgZnJvbSBcIkB2aXRlanMvcGx1Z2luLXZ1ZVwiO1xyXG5pbXBvcnQgeyByZXNvbHZlIH0gZnJvbSBcInBhdGhcIjtcclxuaW1wb3J0IEF1dG9JbXBvcnQgZnJvbSBcInVucGx1Z2luLWF1dG8taW1wb3J0L3ZpdGVcIjtcclxuaW1wb3J0IENvbXBvbmVudHMgZnJvbSBcInVucGx1Z2luLXZ1ZS1jb21wb25lbnRzL3ZpdGVcIjtcclxuaW1wb3J0IHsgRWxlbWVudFBsdXNSZXNvbHZlciB9IGZyb20gXCJ1bnBsdWdpbi12dWUtY29tcG9uZW50cy9yZXNvbHZlcnNcIjtcclxuaW1wb3J0IHsgY3JlYXRlU3ZnSWNvbnNQbHVnaW4gfSBmcm9tICd2aXRlLXBsdWdpbi1zdmctaWNvbnMnXHJcbmltcG9ydCBwYXRoIGZyb20gJ3BhdGgnXHJcbmltcG9ydCB7IHZpdGVaaXAgfSBmcm9tICd2aXRlLXBsdWdpbi16aXAtZmlsZSdcclxuXHJcbmNvbnN0IHVzZVppcCA9IHByb2Nlc3MuZW52LlZJVEVfVVNFX1pJUCA9PT0gJ3RydWUnO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZGVmaW5lQ29uZmlnKHtcclxuICBidWlsZDoge1xyXG4gICAgbWluaWZ5OiAndGVyc2VyJyxcclxuICAgIHNvdXJjZW1hcDogZmFsc2UsIC8vIFx1NEUwRFx1NzUxRlx1NjIxMCBzb3VyY2UgbWFwXHVGRjA4XHU1M0VGXHU1MUNGXHU1QzExXHU0RjUzXHU3OUVGXHVGRjA5XHJcbiAgICB0ZXJzZXJPcHRpb25zOiB7XHJcbiAgICAgIGNvbXByZXNzOiB7XHJcbiAgICAgICAgZHJvcF9jb25zb2xlOiB0cnVlLFxyXG4gICAgICAgIGRyb3BfZGVidWdnZXI6IHRydWUsXHJcbiAgICAgICAgcHVyZV9mdW5jczogWydjb25zb2xlLmxvZyddLCAvLyBcdTVGN0JcdTVFOTVcdTUzQkJcdTk2NjQgY29uc29sZS5sb2dcclxuICAgICAgfSxcclxuICAgICAgZm9ybWF0OiB7XHJcbiAgICAgICAgY29tbWVudHM6IGZhbHNlLCAvLyBcdTUyMjBcdTk2NjRcdTZDRThcdTkxQ0FcclxuICAgICAgfSxcclxuICAgIH0sXHJcbiAgICByb2xsdXBPcHRpb25zOiB7XHJcbiAgICAgIG91dHB1dDoge1xyXG4gICAgICAgIG1hbnVhbENodW5rcyhpZCkge1xyXG4gICAgICAgICAgaWYgKGlkLmluY2x1ZGVzKCd0aHJlZScpKSB7XHJcbiAgICAgICAgICAgIHJldHVybiAndGhyZWUnO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgICAgaWYgKGlkLmluY2x1ZGVzKCdub2RlX21vZHVsZXMnKSkge1xyXG4gICAgICAgICAgICByZXR1cm4gJ3ZlbmRvcic7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfSxcclxuICAgICAgfSxcclxuICAgIH0sXHJcbiAgICBicm90bGlTaXplOiBmYWxzZSwgLy8gXHU1MTczXHU5NUVEIGJyb3RsaSBcdTUyMDZcdTY3OTBcdUZGMENcdTUyQTBcdTVGRUJcdTY3ODRcdTVFRkFcdTkwMUZcdTVFQTZcclxuICB9LFxyXG4gIGNzczoge1xyXG4gICAgbWluaWZ5OiB0cnVlLFxyXG4gICAgcHJlcHJvY2Vzc29yT3B0aW9uczoge1xyXG4gICAgICBzY3NzOiB7XHJcbiAgICAgICAgc2lsZW5jZURlcHJlY2F0aW9uczogW1wibGVnYWN5LWpzLWFwaVwiLCBcImNvbG9yLWZ1bmN0aW9uc1wiXSxcclxuICAgICAgfSxcclxuICAgIH0sXHJcbiAgfSxcclxuICBwbHVnaW5zOiBbXHJcbiAgICB2dWUoKSxcclxuICAgIEF1dG9JbXBvcnQoe1xyXG4gICAgICByZXNvbHZlcnM6IFtFbGVtZW50UGx1c1Jlc29sdmVyKCldLFxyXG4gICAgfSksXHJcbiAgICBDb21wb25lbnRzKHtcclxuICAgICAgcmVzb2x2ZXJzOiBbRWxlbWVudFBsdXNSZXNvbHZlcigpXSxcclxuICAgIH0pLFxyXG4gICAgY3JlYXRlU3ZnSWNvbnNQbHVnaW4oe1xyXG4gICAgICBpY29uRGlyczogW3BhdGgucmVzb2x2ZShwcm9jZXNzLmN3ZCgpLCAnc3JjL2Fzc2V0cy9pbWFnZXMvc3ZnJyldLFxyXG4gICAgICBzeW1ib2xJZDogJ2ljb24tW25hbWVdJ1xyXG4gICAgfSksXHJcbiAgICAuLi4odXNlWmlwID8gW3ZpdGVaaXAoe1xyXG4gICAgICBmb2xkZXJQYXRoOiBwYXRoLnJlc29sdmUoX19kaXJuYW1lLCAnZGlzdCcpLFxyXG4gICAgICBvdXRQYXRoOiBwYXRoLnJlc29sdmUoX19kaXJuYW1lKSxcclxuICAgICAgd2l0aG91dE1haW5Gb2xkZXI6IHRydWUsXHJcbiAgICAgIHppcE5hbWU6ICgoKSA9PiB7XHJcbiAgICAgICAgY29uc3Qgbm93ID0gbmV3IERhdGUoKTtcclxuICAgICAgICBjb25zdCBwYWQgPSBuID0+IG4udG9TdHJpbmcoKS5wYWRTdGFydCgyLCAnMCcpO1xyXG4gICAgICAgIGNvbnN0IHkgPSBub3cuZ2V0RnVsbFllYXIoKTtcclxuICAgICAgICBjb25zdCBtID0gcGFkKG5vdy5nZXRNb250aCgpICsgMSk7XHJcbiAgICAgICAgY29uc3QgZCA9IHBhZChub3cuZ2V0RGF0ZSgpKTtcclxuICAgICAgICBjb25zdCBoID0gcGFkKG5vdy5nZXRIb3VycygpKTtcclxuICAgICAgICBjb25zdCBtaW4gPSBwYWQobm93LmdldE1pbnV0ZXMoKSk7XHJcbiAgICAgICAgY29uc3QgcyA9IHBhZChub3cuZ2V0U2Vjb25kcygpKTtcclxuICAgICAgICByZXR1cm4gYFx1N0VDRlx1ODQyNVx1NTkyN1x1NUM0Ri0ke3l9JHttfSR7ZH0tJHtofSR7bWlufSR7c30uemlwYDtcclxuICAgICAgfSkoKVxyXG4gICAgfSldIDogW10pXHJcbiAgXSxcclxuICByZXNvbHZlOiB7XHJcbiAgICBleHRlbnNpb25zOiBbXCIuanNcIiwgXCIudnVlXCIsIFwiLmpzb25cIiwgXCIuc2Nzc1wiLCBcIi5jc3NcIl0sXHJcbiAgICBhbGlhczoge1xyXG4gICAgICBcIkBcIjogcmVzb2x2ZShfX2Rpcm5hbWUsIFwic3JjXCIpLFxyXG4gICAgfSxcclxuICB9LFxyXG4gIGJhc2U6IFwiLi9cIixcclxuICBzZXJ2ZXI6IHtcclxuICAgIGhvc3Q6IFwiMC4wLjAuMFwiLFxyXG4gICAgaG1yOiB0cnVlLFxyXG4gICAgcHJveHk6IHtcclxuICAgICAgJy9hZG1pbi1hcGknOiB7XHJcbiAgICAgICAgdGFyZ2V0OiAnaHR0cDovLzE5Mi4xNjguMTUuNzQ6MzE4MjInLFxyXG4gICAgICAgIGNoYW5nZU9yaWdpbjogdHJ1ZSxcclxuICAgICAgICByZXdyaXRlOiAocGF0aCkgPT4gcGF0aFxyXG4gICAgICB9LFxyXG4gICAgICAnL29hdXRoJzoge1xyXG4gICAgICAgIHRhcmdldDogJ2h0dHBzOi8vYXBpMi5oaWstY2xvdWQuY29tJywvL1x1NkQ3N1x1NUVCN1x1NEU5MVx1NzI1RlxyXG4gICAgICAgIGNoYW5nZU9yaWdpbjogdHJ1ZSxcclxuICAgICAgfSxcclxuICAgICAgJy92MSc6IHtcclxuICAgICAgICB0YXJnZXQ6ICdodHRwczovL2FwaTIuaGlrLWNsb3VkLmNvbScsLy9cdTZENzdcdTVFQjdcdTRFOTFcdTcyNUZcclxuICAgICAgICBjaGFuZ2VPcmlnaW46IHRydWUsXHJcbiAgICAgIH0sXHJcbiAgICB9XHJcbiAgfSxcclxufSk7XHJcbiJdLAogICJtYXBwaW5ncyI6ICI7QUFBeVcsU0FBUyxvQkFBb0I7QUFDdFksT0FBTyxTQUFTO0FBQ2hCLFNBQVMsZUFBZTtBQUN4QixPQUFPLGdCQUFnQjtBQUN2QixPQUFPLGdCQUFnQjtBQUN2QixTQUFTLDJCQUEyQjtBQUNwQyxTQUFTLDRCQUE0QjtBQUNyQyxPQUFPLFVBQVU7QUFDakIsU0FBUyxlQUFlO0FBUnhCLElBQU0sbUNBQW1DO0FBVXpDLElBQU0sU0FBUyxRQUFRLElBQUksaUJBQWlCO0FBRTVDLElBQU8sc0JBQVEsYUFBYTtBQUFBLEVBQzFCLE9BQU87QUFBQSxJQUNMLFFBQVE7QUFBQSxJQUNSLFdBQVc7QUFBQTtBQUFBLElBQ1gsZUFBZTtBQUFBLE1BQ2IsVUFBVTtBQUFBLFFBQ1IsY0FBYztBQUFBLFFBQ2QsZUFBZTtBQUFBLFFBQ2YsWUFBWSxDQUFDLGFBQWE7QUFBQTtBQUFBLE1BQzVCO0FBQUEsTUFDQSxRQUFRO0FBQUEsUUFDTixVQUFVO0FBQUE7QUFBQSxNQUNaO0FBQUEsSUFDRjtBQUFBLElBQ0EsZUFBZTtBQUFBLE1BQ2IsUUFBUTtBQUFBLFFBQ04sYUFBYSxJQUFJO0FBQ2YsY0FBSSxHQUFHLFNBQVMsT0FBTyxHQUFHO0FBQ3hCLG1CQUFPO0FBQUEsVUFDVDtBQUNBLGNBQUksR0FBRyxTQUFTLGNBQWMsR0FBRztBQUMvQixtQkFBTztBQUFBLFVBQ1Q7QUFBQSxRQUNGO0FBQUEsTUFDRjtBQUFBLElBQ0Y7QUFBQSxJQUNBLFlBQVk7QUFBQTtBQUFBLEVBQ2Q7QUFBQSxFQUNBLEtBQUs7QUFBQSxJQUNILFFBQVE7QUFBQSxJQUNSLHFCQUFxQjtBQUFBLE1BQ25CLE1BQU07QUFBQSxRQUNKLHFCQUFxQixDQUFDLGlCQUFpQixpQkFBaUI7QUFBQSxNQUMxRDtBQUFBLElBQ0Y7QUFBQSxFQUNGO0FBQUEsRUFDQSxTQUFTO0FBQUEsSUFDUCxJQUFJO0FBQUEsSUFDSixXQUFXO0FBQUEsTUFDVCxXQUFXLENBQUMsb0JBQW9CLENBQUM7QUFBQSxJQUNuQyxDQUFDO0FBQUEsSUFDRCxXQUFXO0FBQUEsTUFDVCxXQUFXLENBQUMsb0JBQW9CLENBQUM7QUFBQSxJQUNuQyxDQUFDO0FBQUEsSUFDRCxxQkFBcUI7QUFBQSxNQUNuQixVQUFVLENBQUMsS0FBSyxRQUFRLFFBQVEsSUFBSSxHQUFHLHVCQUF1QixDQUFDO0FBQUEsTUFDL0QsVUFBVTtBQUFBLElBQ1osQ0FBQztBQUFBLElBQ0QsR0FBSSxTQUFTLENBQUMsUUFBUTtBQUFBLE1BQ3BCLFlBQVksS0FBSyxRQUFRLGtDQUFXLE1BQU07QUFBQSxNQUMxQyxTQUFTLEtBQUssUUFBUSxnQ0FBUztBQUFBLE1BQy9CLG1CQUFtQjtBQUFBLE1BQ25CLFVBQVUsTUFBTTtBQUNkLGNBQU0sTUFBTSxvQkFBSSxLQUFLO0FBQ3JCLGNBQU0sTUFBTSxPQUFLLEVBQUUsU0FBUyxFQUFFLFNBQVMsR0FBRyxHQUFHO0FBQzdDLGNBQU0sSUFBSSxJQUFJLFlBQVk7QUFDMUIsY0FBTSxJQUFJLElBQUksSUFBSSxTQUFTLElBQUksQ0FBQztBQUNoQyxjQUFNLElBQUksSUFBSSxJQUFJLFFBQVEsQ0FBQztBQUMzQixjQUFNLElBQUksSUFBSSxJQUFJLFNBQVMsQ0FBQztBQUM1QixjQUFNLE1BQU0sSUFBSSxJQUFJLFdBQVcsQ0FBQztBQUNoQyxjQUFNLElBQUksSUFBSSxJQUFJLFdBQVcsQ0FBQztBQUM5QixlQUFPLDRCQUFRLENBQUMsR0FBRyxDQUFDLEdBQUcsQ0FBQyxJQUFJLENBQUMsR0FBRyxHQUFHLEdBQUcsQ0FBQztBQUFBLE1BQ3pDLEdBQUc7QUFBQSxJQUNMLENBQUMsQ0FBQyxJQUFJLENBQUM7QUFBQSxFQUNUO0FBQUEsRUFDQSxTQUFTO0FBQUEsSUFDUCxZQUFZLENBQUMsT0FBTyxRQUFRLFNBQVMsU0FBUyxNQUFNO0FBQUEsSUFDcEQsT0FBTztBQUFBLE1BQ0wsS0FBSyxRQUFRLGtDQUFXLEtBQUs7QUFBQSxJQUMvQjtBQUFBLEVBQ0Y7QUFBQSxFQUNBLE1BQU07QUFBQSxFQUNOLFFBQVE7QUFBQSxJQUNOLE1BQU07QUFBQSxJQUNOLEtBQUs7QUFBQSxJQUNMLE9BQU87QUFBQSxNQUNMLGNBQWM7QUFBQSxRQUNaLFFBQVE7QUFBQSxRQUNSLGNBQWM7QUFBQSxRQUNkLFNBQVMsQ0FBQ0EsVUFBU0E7QUFBQSxNQUNyQjtBQUFBLE1BQ0EsVUFBVTtBQUFBLFFBQ1IsUUFBUTtBQUFBO0FBQUEsUUFDUixjQUFjO0FBQUEsTUFDaEI7QUFBQSxNQUNBLE9BQU87QUFBQSxRQUNMLFFBQVE7QUFBQTtBQUFBLFFBQ1IsY0FBYztBQUFBLE1BQ2hCO0FBQUEsSUFDRjtBQUFBLEVBQ0Y7QUFDRixDQUFDOyIsCiAgIm5hbWVzIjogWyJwYXRoIl0KfQo=
