# business-display 说明

## projectDetail.vue 图片展示区域优化

### 新的布局结构（2024年更新）

- **左边区域（3/10）**：展示项目详细内容（item1/item2/item3组件）
- **右边区域（7/10）**：专门用于图片展示
  - 右边主要区域：展示当前选中的图片
  - 最右边320px：缩略图列表

### 布局特点

1. **内容与图片分离**：
   - 左侧3/10专门展示项目详细信息
   - 右侧7/10专门展示图片，除了缩略图外都是图片展示区域
   - 点击缩略图可以切换主图

2. **响应式设计**：
   - 使用 flex 布局实现响应式
   - 左侧内容区域支持垂直滚动
   - 右侧图片使用 `object-fit: contain` 保证不变形
   - 缩略图支持滚动查看

3. **交互体验**：
   - 左侧按钮控制内容展示（item1/item2/item3）
   - 右侧缩略图点击切换主图
   - 所有滚动条都隐藏但保留滚动功能

### 技术实现

- 采用 Vue 3 Composition API
- 使用 Flexbox 布局
- 图片预加载和验证机制
- 滚动条自定义样式（隐藏但保留功能）

### 代码结构

```
projectDetail.vue
├── 左侧按钮区域
├── 主要内容区域
│   ├── 左边内容容器 (3/10)
│   │   └── item1/item2/item3 组件
│   └── 右边图片展示区域 (7/10)
│       ├── 右边主要图片容器 (大部分空间)
│       └── 缩略图容器 (固定320px宽度)
└── 返回按钮区域
```

### 布局比例

- 左侧内容：30%
- 右侧图片：70%
  - 主图显示区域：70% - 320px
  - 缩略图区域：320px（固定宽度）

这种布局设计平衡了内容展示和图片展示的需求，左侧专注于项目信息，右侧专注于图片浏览。 