// item3组件专用 - EZUIKit 工具函数
// 由原始工具文件复制而来，避免修改公共工具影响其他组件

/**
 * 安全初始化萤石云播放器
 * @param {Object} options 播放器初始化选项
 * @returns {Object} 播放器实例
 */
export function safeInitEZUIKit(options) {
    if (!window.EZUIKit || typeof window.EZUIKit.EZUIKitPlayer !== 'function') {
        console.error('EZUIKit未正确加载，请检查SDK引入');
        return null;
    }

    try {
        const player = new window.EZUIKit.EZUIKitPlayer(options);

        // 包装player对象，确保所有方法调用安全
        return {
            _player: player,
            play: function () {
                try {
                    if (this._player && typeof this._player.play === 'function') {
                        return this._player.play();
                    }
                } catch (e) {
                    console.error('EZUIKit播放器play方法调用失败:', e);
                }
                return null;
            },
            stop: function () {
                try {
                    if (this._player && typeof this._player.stop === 'function') {
                        return this._player.stop();
                    }
                } catch (e) {
                    console.error('EZUIKit播放器stop方法调用失败:', e);
                }
                return null;
            },
            on: function (event, callback) {
                try {
                    if (this._player && typeof this._player.on === 'function') {
                        return this._player.on(event, callback);
                    } else {
                        console.warn(`EZUIKit播放器不支持on事件: ${event}`);
                        // 如果是error事件，且不支持事件监听，则在控制台输出警告
                        if (event === 'error') {
                            console.warn('EZUIKit播放器不支持错误事件监听，可能影响错误处理');
                        }
                    }
                } catch (e) {
                    console.error(`EZUIKit播放器on方法调用失败(${event}):`, e);
                }
                return null;
            },
            destroy: function () {
                try {
                    if (this._player && typeof this._player.destroy === 'function') {
                        return this._player.destroy();
                    }
                } catch (e) {
                    console.error('EZUIKit播放器destroy方法调用失败:', e);
                }

                // 清理DOM元素
                if (options && options.id) {
                    const container = document.getElementById(options.id);
                    if (container) {
                        container.innerHTML = '';
                    }
                }

                return null;
            },
        };
    } catch (error) {
        console.error('EZUIKit播放器初始化失败:', error);
        return null;
    }
}

/**
 * 安全销毁萤石云播放器
 * @param {Object} player 播放器实例
 */
export function safeDestroyEZUIKit(player) {
    if (!player) return;

    try {
        if (typeof player.destroy === 'function') {
            player.destroy();
        } else if (player._player && typeof player._player.destroy === 'function') {
            player._player.destroy();
        }
    } catch (e) {
        console.warn('销毁播放器实例时出错:', e);
    }
}

/**
 * 修复萤石云播放器卸载错误
 */
export function fixEZUIKitUnloadError() {
    // 防止萤石云SDK报错
    if (window) {
        window.JS_HideWnd = window.JS_HideWnd || function () {
            console.log('JS_HideWnd被调用但已安全处理');
        };

        // 覆盖一些可能导致问题的全局函数
        const noopFn = function () {
            return true;
        };

        // 常见的需要处理的全局函数
        const functionsToFix = [
            'JS_ShowWnd', 'JS_HideWnd', 'JS_DestroyWnd',
            'JS_CreateWnd', 'JS_GetWndRect', 'JS_SetWndRect'
        ];

        functionsToFix.forEach(fnName => {
            if (!window[fnName]) {
                window[fnName] = noopFn;
            }
        });

        // 清除可能的页面卸载处理器
        window.onbeforeunload = null;
    }
}

/**
 * 动态加载萤石云SDK
 * @returns {Promise} 加载成功或失败的Promise
 */
export function loadEZUIKitScript() {
    return new Promise((resolve, reject) => {
        // 如果已经加载，则直接返回
        if (window.EZUIKit && typeof window.EZUIKit.EZUIKitPlayer === 'function') {
            console.log('萤石云SDK已加载');
            resolve(window.EZUIKit);
            return;
        }

        console.log('开始动态加载萤石云SDK');

        // 尝试加载SDK
        const script = document.createElement('script');
        script.type = 'text/javascript';
        script.src = '/ezuikit.js'; // 确保此路径正确指向SDK文件
        script.async = true;

        script.onload = () => {
            console.log('萤石云SDK加载成功');
            // 再次检查确保SDK正确加载
            if (window.EZUIKit && typeof window.EZUIKit.EZUIKitPlayer === 'function') {
                fixEZUIKitUnloadError(); // 修复可能的错误
                resolve(window.EZUIKit);
            } else {
                console.error('萤石云SDK加载后无法找到EZUIKit对象');
                reject(new Error('萤石云SDK加载不完整'));
            }
        };

        script.onerror = () => {
            console.error('萤石云SDK加载失败');
            reject(new Error('萤石云SDK加载失败'));
        };

        document.head.appendChild(script);
    });
}

/**
 * 初始化并确保萤石云播放器可用
 * @returns {Promise} SDK就绪状态的Promise
 */
export async function ensureEZUIKitReady() {
    try {
        // 首先尝试修复可能的错误
        fixEZUIKitUnloadError();

        // 检查SDK是否已加载
        if (window.EZUIKit && typeof window.EZUIKit.EZUIKitPlayer === 'function') {
            return window.EZUIKit;
        }

        // 动态加载SDK
        return await loadEZUIKitScript();
    } catch (error) {
        console.error('确保萤石云SDK就绪失败:', error);
        throw error;
    }
}

/**
 * 清理所有 EZUIKit 相关资源
 */
export function cleanupEZUIKitResources() {
    try {
        // 清理全局变量
        if (window.janus) {
            if (typeof window.janus.destroy === 'function') {
                window.janus.destroy();
            }
            window.janus = null;
        }

        if (window.tts) {
            if (typeof window.tts.destroy === 'function') {
                window.tts.destroy();
            }
            window.tts = null;
        }

        // 清理其他可能的全局对象
        if (window.EZUIKit && window.EZUIKit.instances) {
            Object.keys(window.EZUIKit.instances).forEach(key => {
                const instance = window.EZUIKit.instances[key];
                if (instance && typeof instance.destroy === 'function') {
                    try {
                        instance.destroy();
                    } catch (e) {
                        console.warn(`清理EZUIKit实例${key}时出错:`, e);
                    }
                }
            });
        }

        console.log('EZUIKit资源清理完成');
    } catch (error) {
        console.warn('清理EZUIKit资源时出错:', error);
    }
}

/**
 * 监听页面可见性变化，在页面隐藏时主动清理资源
 */
export function setupEZUIKitVisibilityHandler(player) {
    let isPlayerPaused = false;

    const handleVisibilityChange = () => {
        if (!player) return;

        try {
            if (document.hidden) {
                // 页面隐藏时暂停播放
                if (typeof player.pause === 'function') {
                    player.pause();
                    isPlayerPaused = true;
                }
            } else {
                // 页面显示时恢复播放
                if (isPlayerPaused && typeof player.play === 'function') {
                    player.play();
                    isPlayerPaused = false;
                }
            }
        } catch (error) {
            console.warn('处理页面可见性变化时出错:', error);
        }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    // 返回清理函数
    return () => {
        document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
}

/**
 * 防抖函数 - 用于防止频繁的播放器操作
 */
export function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
} 