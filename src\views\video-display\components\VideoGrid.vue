<template>
  <div class="video-grid" ref="videoGrid">
    <div class="video-row" v-for="(row, rowIndex) in displayedRows" :key="rowIndex">
      <div class="video-item" v-for="(item, colIndex) in row"
        :key="item.id || `${rowIndex}-${colIndex}`"
        :data-project-id="item.isEmpty ? undefined : item.id">
        <div v-if="!item.isEmpty" class="video-bg" :class="{ 'clickable': item.sp }"
          :title="item.sp ? '点击放大视频' : ''" @click="openFullscreenVideo(item)">
          <div class="project-name" :title="getProjectDisplayName(item)">
            {{ getProjectDisplayName(item) }}
          </div>
          <div v-if="item.sp" class="video-player-container-wrapper">
            <div class="fullscreen-overlay">
              <div class="fullscreen-btn">
                <svg viewBox="0 0 24 24" width="24" height="24">
                  <path fill="currentColor"
                    d="M7,14H5v5h5v-2H7V14z M12,10V7H9V5h5v5H12z M14,17h3v-3h2v5h-5V17z M17,10h2V5h-5v2h3V10z" />
                </svg>
              </div>
            </div>
            <!-- <div v-if="playerLoadingStates.get(getPlayerId(item))" class="player-loading-overlay">
              <div class="loading-spinner-small"></div>
              <div class="loading-text-small">正在加载视频...</div>
            </div> -->
            <div v-if="playerErrorStates.get(getPlayerId(item))" class="player-error-overlay">
              <div class="error-icon">⚠️</div>
              <div class="error-text-small">视频加载失败</div>
            </div>
            <div :id="getPlayerId(item)" class="video-player-container"></div>
          </div>
          <div v-else class="no-video-placeholder">
            <div class="placeholder-icon">🎬</div>
            <div class="placeholder-text">暂无视频</div>
          </div>
        </div>
        <div v-else class="video-bg empty-project">
          <div class="project-name" title="等待项目">等待项目</div>
          <div class="empty-video-placeholder">
            <div class="placeholder-icon">🎬</div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="projectList.length === 0 && !loading" class="empty-state">
      <div class="empty-text">暂无数据</div>
    </div>
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <p class="loading-text">正在加载数据...</p>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, nextTick, computed, watch } from 'vue'
import playerManager from '@/utils/playerManagers.js';

const props = defineProps({
  projectList: {
    type: Array,
    default: () => []
  },
  type: {
    type: String,
    required: true // 'monitor' | 'international' | 'major'
  },
  loading: {
    type: Boolean,
    default: false
  },
  itemsPerRow: {
    type: Number,
    default: 4
  }
})

const emit = defineEmits(['fullscreen'])

const playerLoadingStates = ref(new Map())
const playerErrorStates = ref(new Map())
const videoGrid = ref(null)
const observer = ref(null)

const displayedRows = computed(() => {
  const rows = []
  const itemsPerRow = props.itemsPerRow
  const projects = props.projectList
  if (!projects || projects.length === 0) {
    return []
  }
  for (let i = 0; i < projects.length; i += itemsPerRow) {
    const row = projects.slice(i, i + itemsPerRow)
    rows.push(row)
  }
  const lastRow = rows[rows.length - 1]
  if (lastRow && lastRow.length < itemsPerRow) {
    const placeholdersToAdd = itemsPerRow - lastRow.length
    for (let i = 0; i < placeholdersToAdd; i++) {
      lastRow.push({
        id: `empty-${rows.length - 1}-${lastRow.length}`,
        isEmpty: true
      })
    }
  }
  return rows
})

// 格式化时间到年月
const formatTimeToYearMonth = (timeString) => {
  if (!timeString) return ''
  try {
    // 时间格式示例: "2025-04-23 21:34:27"
    const date = new Date(timeString)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    return `${year}-${month}`
  } catch (error) {
    console.error('时间格式化失败:', error)
    return ''
  }
}

// 获取显示的项目名称（对于国际工程添加时间）
const getProjectDisplayName = (item) => {
  const baseName = item.xmjc || item.xmmc || item.name
  
  // 如果是国际工程类型且有时间信息，添加时间后缀
  if (props.type === 'international' && item.time) {
    const timeFormat = formatTimeToYearMonth(item.time)
    return timeFormat ? `${baseName}-${timeFormat}` : baseName
  }
  
  // 如果是重点工程类型且有createTime信息，添加时间后缀
  if (props.type === 'major' && item.createTime) {
    const timeFormat = formatTimeToYearMonth(item.createTime)
    return timeFormat ? `${baseName}-${timeFormat}` : baseName
  }
  
  return baseName
}

function getPlayerId(item) {
  if (props.type === 'monitor') {
    return `monitor-player-${item.deviceSerial}-${item.channelNo}`
  } else if (props.type === 'international') {
    return `international-player-${item.id}`
  } else if (props.type === 'major') {
    return `major-player-${item.id}`
  }
  return ''
}

const setupPlayerObserver = () => {
  if (observer.value) observer.value.disconnect();
  const options = {
    root: videoGrid.value,
    rootMargin: '0px 0px 200px 0px',
    threshold: 0.1,
  };
  observer.value = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        const target = entry.target;
        const projectId = target.dataset.projectId;
        if (!projectId) return;
        const item = props.projectList.find((p) => p.id === projectId);
        if (item && item.sp) {
          const playerId = getPlayerId(item);
          if (!playerManager.hasPlayer(playerId) && !playerLoadingStates.value.has(playerId)) {
            initializePlayer(item);
          }
        }
        observer.value.unobserve(target);
      }
    });
  }, options);
  if (videoGrid.value) {
    const elementsToObserve = videoGrid.value.querySelectorAll('.video-item[data-project-id]');
    elementsToObserve.forEach((el) => {
      observer.value.observe(el);
    });
  }
};

const initializePlayer = async (item) => {
  const playerId = getPlayerId(item);
  await playerManager.initPlayer({
    data: item,
    type: props.type,
    onLoadingChange: (playerId, isLoading) => {
      if (isLoading) {
        playerLoadingStates.value.set(playerId, true);
      } else {
        playerLoadingStates.value.delete(playerId);
      }
    },
    onError: (playerId, error) => {
      playerErrorStates.value.set(playerId, error.message);
      playerLoadingStates.value.delete(playerId);
    }
  });
};

const openFullscreenVideo = (item) => {
  // debugger
  emit('fullscreen', item)
}

onMounted(() => {
  setupPlayerObserver()
})

onBeforeUnmount(() => {
  if (observer.value) observer.value.disconnect();
  playerManager.destroyAllPlayers(props.type);
})

watch(() => props.projectList, async () => {
  await nextTick();
  setupPlayerObserver();
}, { deep: true })
</script>

<style lang="scss" scoped>
/* 可点击的视频项目样式 */
.video-bg.clickable {
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.video-bg.clickable:hover {
  transform: scale(1.03);
  box-shadow: 0 6px 20px rgba(0, 255, 255, 0.4);
}

/* 放大按钮覆盖层 */
.fullscreen-overlay {
  position: absolute;
  top: 5px;
  right: 5px;
  z-index: 15;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.video-bg.clickable:hover .fullscreen-overlay {
  opacity: 1;
}

.fullscreen-btn {
  width: 32px;
  height: 32px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #00FFFF;
  transition: all 0.3s ease;
}

.fullscreen-btn:hover {
  background: rgba(0, 255, 255, 0.2);
  transform: scale(1.1);
}

.fullscreen-btn svg {
  width: 18px;
  height: 18px;
}

/* 全屏弹窗样式 */
.fullscreen-modal {
  position: fixed;
  margin: 0 auto;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  animation: fadeIn 0.3s ease;
}
.video-grid {
  width: 100%;
  height: calc(100% - 80px);
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 15px;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
  &::-webkit-scrollbar {
    width: 6px;
  }
  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
  }
  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    &:hover {
      background: rgba(255, 255, 255, 0.5);
    }
  }
}
.video-row {
  min-height: 120px;
  display: flex;
  gap: 15px;
  flex-shrink: 0;
}
.video-item {
  flex: 1;
  aspect-ratio: 16/9;
  min-height: 140px;
  border-radius: 8px;
  // overflow: hidden;
}
.video-bg {
  width: 100%;
  height: 100%;
  display: block;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  padding: 0;
  cursor: pointer;
  transition: all 0.3s ease;
  &:hover {
    transform: scale(1.02);
    box-shadow: 0 4px 12px rgba(0, 255, 255, 0.3);
  }
}
.project-name {
  position: absolute;
  bottom: -10px;
  right: 0;
  z-index: 5;
  width: 50%;
  height: auto;
  line-height: 1.4;
  padding: 8px 12px;
  box-sizing: border-box;
  // background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0) 100%);
  display: block;
  font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 24px;
  color: #00FFFF;
  text-align: right;
  font-style: normal;
  text-transform: none;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.video-player-container {
  width: 100%;
  height: 100%;
  background: #000;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 确保EZUIKit播放器保持固定尺寸不被拉伸 */
.video-player-container :deep(.ezuikit-player) {
  max-width: 100% !important;
  max-height: 100% !important;
  object-fit: contain !important;
}

.video-player-container :deep(video) {
  max-width: 100% !important;
  max-height: 100% !important;
  object-fit: contain !important;
}
.no-video-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2px dashed rgba(255, 255, 255, 0.3);
}
.placeholder-text {
  color: rgba(255, 255, 255, 0.6);
  font-size: 11px;
  text-align: center;
  margin-top: 4px;
}
.empty-project {
  opacity: 0.3;
  pointer-events: none;
}
.empty-video-placeholder {
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed rgba(255, 255, 255, 0.2);
}
.placeholder-icon {
  font-size: 30px;
  opacity: 0.5;
}
.loading-text-small {
  color: #00FFFF;
  font-size: 10px;
  font-weight: 500;
  margin-top: 4px;
  text-align: center;
}

.error-text-small {
  color: #FF6B6B;
  font-size: 10px;
  font-weight: 500;
  margin-top: 4px;
  text-align: center;
}
.loading-spinner-small {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(0, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #00FFFF;
  animation: spin 1s ease-in-out infinite;
  margin-right: 10px;
}
.video-player-container-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.fullscreen-overlay {
  position: absolute;
  top: 5px;
  right: 5px;
  z-index: 15;
  opacity: 0;
  transition: opacity 0.3s ease;
}
.video-bg.clickable:hover .fullscreen-overlay {
  opacity: 1;
}
.fullscreen-btn {
  width: 32px;
  height: 32px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #00FFFF;
  transition: all 0.3s ease;
}
.fullscreen-btn:hover {
  background: rgba(0, 255, 255, 0.2);
  transform: scale(1.1);
}
.fullscreen-btn svg {
  width: 18px;
  height: 18px;
}
.player-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: 4px;
}
.player-error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(40, 44, 52, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: 4px;
  border: 1px solid rgba(244, 67, 54, 0.3);
}
.error-icon {
  font-size: 20px;
  opacity: 0.8;
  margin-bottom: 2px;
}
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: rgba(255, 255, 255, 0.6);
}
.empty-text {
  font-size: 18px;
  font-weight: 500;
}
.loading-overlay {
  position: absolute;
  top: 80px;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.1);
  z-index: 10;
}
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #00FFFF;
  animation: spin 1s ease-in-out infinite;
}
.loading-text {
  margin-top: 15px;
  color: #00FFFF;
  font-size: 16px;
  font-weight: 500;
}
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
</style>
