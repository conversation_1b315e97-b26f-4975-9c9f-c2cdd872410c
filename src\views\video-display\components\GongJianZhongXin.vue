<template>
  <div class="video-grid">
    <div class="video-row" v-for="(row, rowIndex) in deviceRows" :key="rowIndex">
      <div class="video-item" v-for="(device, colIndex) in row"
        :key="device ? device.nvrSerialNo + '-' + device.cameraChannel : colIndex">
        <div class="monitor-bg" v-if="device">
          <div class="monitor-name" :title="device.projectName">
            {{ device.projectName || '公建中心项目' + (rowIndex * 4 + colIndex + 1) }}
          </div>
          
          <!-- 特定项目使用TpCellPlayer -->
          <div v-if="shouldUseTpCellPlayer(device)" class="tp-cell-player-container">
            <TpCellPlayer :cell="getTpCellData(device, rowIndex * 4 + colIndex)" />
          </div>
          
          <!-- 其他项目使用EZUIKit播放器 -->
          <div v-else :id="'videoPlayer-gjzx-' + (rowIndex * 4 + colIndex)" class="video-player"></div>
        </div>
        <div class="monitor-bg empty-monitor" v-else>
          <div class="monitor-name" title="等待项目">等待项目</div>
          <div class="empty-monitor-placeholder">
            <div class="placeholder-icon">📷</div>
          </div>
        </div>
      </div>
    </div>

    <div v-if="deviceList.length === 0 && !loading" class="empty-state">
      <div class="empty-text">暂无数据</div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, nextTick, computed, watch } from 'vue'
import axios from 'axios';
import TpCellPlayer from '@/components/TpCellPlayer.vue'

const props = defineProps({
  deviceList: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})
const myPlayer = ref([])
const cloudTokens = ref({}) // 改为对象，存储不同项目的token
const loading = ref(false)

// 特殊项目ID列表（使用集团token方式）
const specialProjectIds = [
  "ce247b86047543ba810996088ee94bec",
  "ffbedcedc0094a3c89ee708ddb00c43d",
]

// 使用TpCellPlayer的项目ID
const tpCellPlayerProjectId = "0a51891dc1724a69ac6a13d738f61491"

// 老key项目列表（使用老萤石云key）
const oldProjectIdList = [
  "67fafd43bd994d20af6e0c79a688b2d5",
  "c0cf632558aa4c449bcf060267b6b714", 
  "10e1d0d7c3f441d28755e17c032f2488",
  "1521c317f19b4d26b0d9c234ab21e1a6",
  "41b50d2e99794c68bb251b79d6a38784",
  "8647ecca2a6d4a4aac18ed5aeaa21bcf",
  "e11aa784d17c4be093422299a1c6e171",
  "ce247b86047543ba810996088ee94bec",
  "ffbedcedc0094a3c89ee708ddb00c43d",
  "6f264d41a4e547958f4a7f676dddcc2d",
  "0a51891dc1724a69ac6a13d738f61491"
]

// 默认配置（匠心项目）
const defaultConfig = {
  client_id: 'd1b4e2d1e64c4a3eaf6a288d6e4215f1',
  client_secret: '723f7d81ef8b4f70a236ff6514bb458e'
}

// 集团配置
const jituanConfig = {
  client_id: "3f549aacc908408caf2e38e862d700a2",
  client_secret: "297208596ad34bb09b97170fe9944ffe"
}

// 老萤石云key配置
const oldEzvizConfig = {
  appKey: "be1a1dd0ef6548d0a525fc67eea7f7e9",
  appSecret: "844143694ed21d37ea19b7cc54685ca3"
}

// 新萤石云key配置
const newEzvizConfig = {
  appKey: "a40cfb165af04e868e25f1fdfd859992",
  appSecret: "c4bcf6057802207e02ae0421ccc57eec"
}

// 获取集团token（参考getJituanToken方法）
const getJituanToken = async () => {
  try {
    console.log('🔑 获取集团token...')
    const { client_id, client_secret } = jituanConfig
    const grant_type = "client_credentials"
    
    const res1 = await axios.post(`/oauth/token?grant_type=${grant_type}&client_id=${client_id}&client_secret=${client_secret}`)
    const access_token = res1?.data?.access_token
    
    if (!access_token) {
      console.error('❌ 获取access_token失败')
      return null
    }
    
    const res2 = await axios.get(`/v1/ezviz/account/info`, {
      headers: { Authorization: 'Bearer ' + access_token }
    })
    
    const token = res2?.data?.data?.token
    if (token) {
      console.log('✅ 集团token获取成功')
      cloudTokens.value['jituan'] = token
      localStorage.setItem('cloudToken_jituan', token)
      return token
    }
  } catch (error) {
    console.error('❌ 获取集团token失败:', error)
  }
  return null
}

// 获取默认token（匠心项目）
const getDefaultToken = async () => {
  try {
    console.log('🔑 获取默认token...')
    const { client_id, client_secret } = defaultConfig
    const grant_type = "client_credentials"
    
    const res1 = await axios.post(`/oauth/token?grant_type=${grant_type}&client_id=${client_id}&client_secret=${client_secret}`)
    const access_token = res1?.data?.access_token
    
    if (!access_token) {
      console.error('❌ 获取access_token失败')
      return null
    }
    
    const res2 = await axios.get(`/v1/ezviz/account/info`, {
      headers: { Authorization: 'Bearer ' + access_token }
    })
    
    const token = res2?.data?.data?.token
    if (token) {
      console.log('✅ 默认token获取成功')
      cloudTokens.value['default'] = token
      localStorage.setItem('cloudToken_default', token)
      return token
    }
  } catch (error) {
    console.error('❌ 获取默认token失败:', error)
  }
  return null
}

// 使用老萤石云key获取token（参考setOldKey方法）
const getOldEzvizToken = async () => {
  try {
    console.log('🔑 获取老萤石云token...')
    const { appKey, appSecret } = oldEzvizConfig
    
    const response = await axios.post(`https://open.ys7.com/api/lapp/token/get?appKey=${appKey}&appSecret=${appSecret}`)
    
    if (response.data && response.data.code === '200') {
      const token = response.data.data.accessToken
      console.log('✅ 老萤石云token获取成功')
      cloudTokens.value['oldEzviz'] = token
      localStorage.setItem('cloudToken_oldEzviz', token)
      return token
    } else {
      console.error('❌ 老萤石云API返回错误:', response.data)
    }
  } catch (error) {
    console.error('❌ 获取老萤石云token失败:', error)
  }
  return null
}

// 使用新萤石云key获取token（参考setNewKey方法）
const getNewEzvizToken = async () => {
  try {
    console.log('🔑 获取新萤石云token...')
    const { appKey, appSecret } = newEzvizConfig
    
    const response = await axios.post(`https://open.ys7.com/api/lapp/token/get?appKey=${appKey}&appSecret=${appSecret}`)
    
    if (response.data && response.data.code === '200') {
      const token = response.data.data.accessToken
      console.log('✅ 新萤石云token获取成功')
      cloudTokens.value['newEzviz'] = token
      localStorage.setItem('cloudToken_newEzviz', token)
      return token
    } else {
      console.error('❌ 新萤石云API返回错误:', response.data)
    }
  } catch (error) {
    console.error('❌ 获取新萤石云token失败:', error)
  }
  return null
}

// 过滤出普通项目（排除特殊项目）
const normalDeviceList = computed(() => {
  const result = props.deviceList.filter(device => 
    !specialProjectIds.includes(device.projectId)
  )
  console.log('🔍 普通项目设备:', result.length, result)
  return result
})

// 过滤出特殊项目
const specialDeviceList = computed(() => {
  const result = props.deviceList.filter(device => 
    specialProjectIds.includes(device.projectId)
  )
  console.log('⭐ 特殊项目设备:', result.length, result)
  console.log('📋 所有设备的projectId:', props.deviceList.map(d => d.projectId))
  return result
})

// 合并后的完整设备列表（普通项目 + 特殊项目）
const finalDeviceList = computed(() => {
  const result = [...normalDeviceList.value, ...specialDeviceList.value]
  console.log('🎯 最终设备列表:', result.length, result)
  return result
})

// 按4个一行分组，最多16个
const deviceRows = computed(() => {
  const perRow = 4
  const maxDevices = 16
  const arr = []
  const list = finalDeviceList.value.slice(0, maxDevices)
  
  for (let i = 0; i < maxDevices; i += perRow) {
    const rowDevices = list.slice(i, i + perRow)
    // 补空位到4个
    while (rowDevices.length < perRow) {
      rowDevices.push(null)
    }
    arr.push(rowDevices)
  }
  
  return arr
})

// 判断是否应该使用TpCellPlayer
const shouldUseTpCellPlayer = (device) => {
  return device.projectId === tpCellPlayerProjectId
}

// 获取TpCellPlayer的数据格式
const getTpCellData = (device, index) => {
  if (!device || device.projectId !== tpCellPlayerProjectId) {
    return { url: '' }
  }
  
  // 使用设备的cameraUrl字段，如果没有则使用固定URL作为备用
  const hlsUrl = device.cameraUrl || 'http://video.xhs-sz.com:83/openUrl/4R1nBfi/live.m3u8'
  
  console.log(`📹 TpCellPlayer设备 ${index} URL:`, hlsUrl)
  
  return {
    url: hlsUrl,
    name: device.projectName || `公建中心项目${index + 1}`
  }
}



// 初始化所有token
const initAllTokens = async () => {
  console.log('🚀 开始初始化所有token...')
  console.log('📊 设备总数:', props.deviceList.length)
  console.log('📊 普通项目数:', normalDeviceList.value.length)
  console.log('📊 特殊项目数:', specialDeviceList.value.length)
  console.log('📊 最终设备列表:', finalDeviceList.value.length)
  console.log('📋 设备详情:', finalDeviceList.value)
  
  if (props.deviceList.length === 0) {
    console.log('⚠️  设备列表为空，跳过token初始化')
    return
  }
  
  // 获取默认token（普通项目使用）
  await getDefaultToken()
  
  // 获取集团token（特殊项目使用）
  if (specialDeviceList.value.length > 0) {
    await getJituanToken()
  }
  
  // 获取萤石云token（用于直接萤石云方式）
  await getOldEzvizToken()
  await getNewEzvizToken()
  
  console.log('🎬 初始化播放器...')
  // 初始化所有播放器
  initAllPlayers()
}

// 初始化所有播放器
const initAllPlayers = () => {
  const devices = finalDeviceList.value.slice(0, 16)
  
  console.log('🎯 开始初始化播放器...')
  console.log('📋 将要初始化的设备数量:', devices.length)
  console.log('📋 设备列表:', devices)
  
  if (devices.length === 0) {
    console.log('⚠️  没有设备需要初始化播放器')
    return
  }
  
  // 初始化所有播放器
  devices.forEach((item, idx) => {
    if (shouldUseTpCellPlayer(item)) {
      console.log(`📺 设备 ${idx} 使用TpCellPlayer: ${item.projectName}`)
    } else {
      // 使用EZUIKit播放器
      console.log(`📺 设备 ${idx} 使用EZUIKit播放器: ${item.projectName}`)
      setUrl(item, idx)
    }
  })
  
  console.log('✅ 播放器初始化完成')
}

// setUrl实现播放器初始化
envCheckEZUIKit()
function envCheckEZUIKit() {
  if (typeof window !== 'undefined' && !window.EZUIKit) {
    // eslint-disable-next-line no-console
    console.warn('EZUIKit 未全局引入，播放器无法初始化')
  }
}
const setUrl = async (item, index) => {
  console.log(`🎬 初始化播放器 ${index}:`, item)
  if (!item) {
    console.log(`⏭️ 跳过空设备 ${index}`)
    return
  }
  
  // 销毁旧播放器
  if (myPlayer.value[index]) {
    console.log(`🗑️ 销毁旧播放器 ${index}`)
    myPlayer.value[index].destroy && myPlayer.value[index].destroy()
    myPlayer.value[index] = null
  }

  let accessToken = ''
  let isSpecialProject = false
  
  // 根据设备的accessMethod和项目ID来选择token
  if (item.accessMethod === "1001001") {
    // 萤石云方式
    console.log(`📡 设备 ${index} 使用萤石云方式`)
    
    if (oldProjectIdList.includes(item.projectId)) {
      // 使用老萤石云key
      console.log(`🔑 设备 ${index} 使用老萤石云key`)
      accessToken = cloudTokens.value['oldEzviz'] || localStorage.getItem('cloudToken_oldEzviz') || ''
      if (!accessToken) {
        console.log('🔄 重新获取老萤石云token')
        accessToken = await getOldEzvizToken()
      }
    } else {
      // 使用新萤石云key
      console.log(`🔑 设备 ${index} 使用新萤石云key`)
      accessToken = cloudTokens.value['newEzviz'] || localStorage.getItem('cloudToken_newEzviz') || ''
      if (!accessToken) {
        console.log('🔄 重新获取新萤石云token')
        accessToken = await getNewEzvizToken()
      }
    }
  } else {
    // EZUIKit方式，根据项目类型选择token
    if (specialProjectIds.includes(item.projectId)) {
      // 特殊项目使用集团token
      console.log(`⭐ 设备 ${index} 是特殊项目，使用集团token:`, item.projectId)
      isSpecialProject = true
      accessToken = cloudTokens.value['jituan'] || localStorage.getItem('cloudToken_jituan') || ''
      
      if (!accessToken) {
        console.log(`🔄 特殊项目 ${item.projectId} 需要重新获取集团token`)
        accessToken = await getJituanToken()
      }
    } else {
      // 普通项目使用默认token
      console.log(`🔵 设备 ${index} 是普通项目，使用默认token:`, item.projectId || '无projectId')
      accessToken = cloudTokens.value['default'] || localStorage.getItem('cloudToken_default') || ''
      
      if (!accessToken) {
        console.log('🔄 默认项目需要重新获取token')
        accessToken = await getDefaultToken()
      }
    }
  }

  console.log(`🔑 设备 ${index} token状态:`, accessToken ? '有token' : '无token')
  console.log(`🔧 访问方式: ${item.accessMethod || '默认'}`)
  console.log(`🔧 特殊项目: ${isSpecialProject ? '是' : '否'}`)

  if (!window.EZUIKit) {
    console.error('❌ EZUIKit未加载')
    return
  }
  
  if (!accessToken) {
    console.error(`❌ 设备 ${index} 无token，跳过初始化`)
    return
  }
  
  console.log(`🎯 开始创建播放器 ${index}...`)
  
  // EZUIKit主题配置
  const ezuikitTheme = {
    autoFocus: 5,
    poster: "https://resource.eziot.com/group1/M00/00/89/CtwQEmLl8r-AZU7wAAETKlvgerU237.png",
    header: {
      color: "#1890ff",
      activeColor: "#FFFFFF",
      backgroundColor: "#000000",
      btnList: []
    },
    footer: {
      color: "#FFFFFF",
      activeColor: "#1890FF",
      backgroundColor: "#00000051",
      btnList: [
        {
          iconId: "play",
          part: "left",
          defaultActive: 1,
          memo: "播放",
          isrender: 1,
        },
        {
          iconId: "capturePicture",
          part: "left",
          defaultActive: 0,
          memo: "截屏按钮",
          isrender: 1,
        },
        {
          iconId: "sound",
          part: "left",
          defaultActive: 0,
          memo: "声音按钮",
          isrender: 1,
        },
        {
          iconId: "recordvideo",
          part: "left",
          defaultActive: 0,
          memo: "录制按钮",
          isrender: 1,
        },
        {
          iconId: "expend",
          part: "left",
          defaultActive: 0,
          memo: "全局全屏按钮",
          isrender: 1,
        },
      ],
    },
  };
  
  try {
    const playerId = 'videoPlayer-gjzx-' + index
    const playerUrl = `ezopen://open.ys7.com/${item.nvrSerialNo}/${item.cameraChannel}.hd.live`
    
    console.log(`🆔 播放器ID: ${playerId}`)
    console.log(`🔗 播放器URL: ${playerUrl}`)
    
    myPlayer.value[index] = new window.EZUIKit.EZUIKitPlayer({
      staticPath: '/ezuikit_static/v65',
      id: playerId,
      accessToken: accessToken,
      url: playerUrl,
      audio: false,
      width: 860,
      height: 430,
      useHardDev: false,
      themeData: ezuikitTheme,
    })
    
    console.log(`✅ 播放器 ${index} 创建成功`)
  } catch (error) {
    console.error(`❌ 播放器 ${index} 创建失败:`, error)
  }
}

// 状态样式和文本
const getStatusClass = (status) => {
  switch (status) {
    case '1': return 'online';
    case '0': return 'offline';
    case '-1': return 'error';
    default: return 'unknown';
  }
}
const getStatusText = (status) => {
  switch (status) {
    case '1': return '在线';
    case '0': return '离线';
    case '-1': return '异常';
    default: return '未知';
  }
}

onMounted(() => {
  console.log('🎬 GongJianZhongXin 组件已挂载，设备数量:', props.deviceList.length)
  initAllTokens()
})

onBeforeUnmount(() => {
  myPlayer.value.forEach(player => {
    player && player.destroy && player.destroy()
  })
})

// 监听设备列表变化，重新初始化播放器
watch(() => props.deviceList, async (newList, oldList) => {
  console.log('📝 公建中心设备列表变化:', {
    oldCount: oldList?.length || 0,
    newCount: newList?.length || 0
  })
  
  if (newList && newList.length > 0) {
    console.log('🔄 设备列表更新，重新初始化播放器...')
    
    // 销毁现有播放器
    myPlayer.value.forEach(player => {
      if (player && player.destroy) {
        player.destroy()
      }
    })
    myPlayer.value = []
    
    // 等待DOM更新后重新初始化
    await nextTick()
    initAllTokens()
  }
}, { immediate: false, deep: true })
</script>

<style scoped>
.video-grid {
  width: 100%;
  height: calc(100% - 80px);
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 15px;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
}

.video-row {
  min-height: 120px;
  display: flex;
  gap: 15px;
  flex-shrink: 0;
}

.video-item {
  flex: 1;
  aspect-ratio: 16/9;
  min-height: 140px;
  border-radius: 8px;
  overflow: hidden;
}

.monitor-bg {
  width: 100%;
  height: 100%;
  display: block;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  padding: 0;
  transition: all 0.3s ease;
  /* background: #0c2538; */
  /* border: 2px solid #636771; */
}

.monitor-bg:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 255, 255, 0.3);
}

.monitor-name {
  position: absolute;
  bottom: 20px;
  right: 0;
  z-index: 5;
  width: 50%;
  height: auto;
  padding: 8px 12px;
  box-sizing: border-box;
  /* background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0) 100%); */
  display: block;
  font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 24px;
  color: #00FFFF;
  text-align: right;
  font-style: normal;
  text-transform: none;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.monitor-info {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 4px;
  font-size: 10px;
  width: 100%;
  justify-content: center;
  flex-wrap: wrap;
  position: absolute;
  bottom: 8px;
  left: 0;
  z-index: 6;
}

.device-serial,
.channel-no {
  background: rgba(0, 0, 0, 0.5);
  color: rgba(255, 255, 255, 0.8);
  padding: 1px 4px;
  border-radius: 2px;
  font-size: 9px;
}

.status-indicator {
  padding: 1px 4px;
  border-radius: 8px;
  font-size: 9px;
  font-weight: 500;
}

.status-indicator.online {
  background: rgba(76, 175, 80, 0.3);
  color: #4CAF50;
}

.status-indicator.offline {
  background: rgba(255, 152, 0, 0.3);
  color: #FF9800;
}

.status-indicator.error {
  background: rgba(244, 67, 54, 0.3);
  color: #F44336;
}

.status-indicator.unknown {
  background: rgba(158, 158, 158, 0.3);
  color: #9E9E9E;
}

.video-player {
  width: 100%;
  height: 100%;
  border-radius: 4px;
  overflow: hidden;
}

.tp-cell-player-container {
  width: 100%;
  height: 100%;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.tp-cell-player-container :deep(.player) {
  width: 100%;
  height: 100%;
  border-radius: 4px;
  overflow: hidden;
}

.tp-cell-player-container :deep(.player video) {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.empty-monitor {
  opacity: 0.3;
  pointer-events: none;
}

.empty-monitor-placeholder {
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed rgba(255, 255, 255, 0.2);
  flex-shrink: 0;
}

.placeholder-icon {
  font-size: 30px;
  opacity: 0.5;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: rgba(255, 255, 255, 0.6);
}

.empty-text {
  font-size: 18px;
  font-weight: 500;
}

.loading-overlay {
  position: absolute;
  top: 80px;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #00FFFF;
  animation: spin 1s ease-in-out infinite;
}

.loading-text {
  margin-top: 15px;
  color: #00FFFF;
  font-size: 16px;
  font-weight: 500;
}
</style>

