@tailwind base;
@tailwind components;
@tailwind utilities;

body,
html {
  width: 100%;
  height: 100%;
  color: white;
  margin: 0;
}
#app {
  width: 1920px;
  height: 1080px;
  color: #fff;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
  font-family: <PERSON><PERSON><PERSON>d<PERSON>umber, MicrosoftYaHei;
}

.font-family-youshebiaotihei {
  font-family: YouSheBiaoTiHei;
}

.font-family-pingfang {
  font-family: pingfang;
}

.font-family-microsoftyahei {
  font-family: MicrosoftYaHei;
}

.font-family-alimama {
  font-family: Alimama;
}

.font-family-oswald-medium {
  font-family: Oswald Medium;
}

.font-family-pangmenzhengdao {
  font-family: PangMenZhengDao;
}

.font-family-tcloudnumber {
  font-family: TCloudNumber;
}

.jindu-container-green {
  background: url("@/assets/images/zl/xiegang.png") repeat-x center center,
    linear-gradient(to right, #0082ff, #00b6ff, #00f0ff);
}
.jindu-container-red {
  background: url("@/assets/images/zl/xiegang.png") repeat-x center center,
    linear-gradient(to right, #da3832, #f0b354);
}

.overflow-scrollbar {
  overflow-y: auto;
}
/*滚动条样式*/
.overflow-scrollbar::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 4px; /*高宽分别对应横竖滚动条的尺寸*/
  height: 4px;
}
.overflow-scrollbar::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: rgba(0, 0, 0, 0.2);
}
.overflow-scrollbar::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 0;
  background: rgba(0, 0, 0, 0.1);
}

.el-overlay {
  pointer-events: all;
}
