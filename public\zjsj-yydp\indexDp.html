<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="referrer" content="no-referrer" />
    <title>中江运营大屏</title>
    <!-- <link href="css/index.css" rel="stylesheet" /> -->
    <!-- <script type="text/javascript" src="http://gis.ztmapinfo.com/lib/ztmap3d-lib.js" include="jquery,ztmapgl,ztmapglthree,ztmapglthreeloader"></script> -->
    <script src="js/jquery-3.7.1.min.js"></script>

    <script src="js/three.min.js"></script>
    <script src="js/OrbitControls.js"></script>
    <script src="js/CSS2DRenderer.js"></script>
    <script src="js/CSS3DRenderer.js"></script>
    <script src="js/render.js"></script>
    <script src="js/stats.min.js"></script>
    <script src="js/Events.js"></script>
    <script src="js/animate.js"></script>
    <!-- <script src="loader20250306/ZTMapModels-new.min.js"></script> -->
    <script src="js/turf.js"></script>
    <!-- <script src="https://unpkg.com/vconsole@latest/dist/vconsole.min.js"></script> -->
    <script src="js/indexDp.js"></script>
    <style>
        body,
        html {
            height: 100%;
            width: 100%;
            margin: 0;
            padding: 0;
            border: none;
            overflow: hidden;
        }
        body{
            background: linear-gradient(0deg, transparent, #afd9e7);
            background: url(img/bg-new.png) no-repeat;
            background-size: 100% 100%;
        }
        body[code="plan"]{
            background: url(img/bg-new.png) no-repeat;
            background-size: 100% 100%;
        }
        ::-webkit-scrollbar {
            width: 3px; /*滚动条宽度*/
            height: 10px; /*滚动条高度*/
        } /*定义滚动条轨道 内阴影+圆角*/
        ::-webkit-scrollbar-track {
            -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
            border-radius: 10px; /*滚动条的背景区域的圆角*/
            background-color: #244f8327; /*滚动条的背景颜色*/
        } /*定义滑块 内阴影+圆角*/
        ::-webkit-scrollbar-thumb {
            border-radius: 10px; /*滚动条的圆角*/
            -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
            background-color: #4f97f415; /*滚动条的背景颜色*/
        }
        #map {
            width: 100%;
            height: 100%;
            margin: 0;
            padding: 0;
            overflow: hidden;
            position: relative;
        }
        .cir1{
            position: absolute;
            bottom: 10%;
            width: 50%;
            left: 25%;
            animation: showAnimate 1.55s infinite linear alternate; /* 动画名称，持续时间，无限循环 */
            opacity: 0;
            filter: blur(3px);
            display: none;
        }
        @keyframes showAnimate {
            0% { opacity:0 }
            /* 50% { opacity:1 } */
            100% { opacity:1 }
        }
        .cir2{
            position: absolute;
            bottom: 10%;
            width: 50%;
            left: 25%;
            animation: sacleAnimate 3.1s infinite linear; /* 动画名称，持续时间，无限循环 */
            opacity: 0;
            filter: blur(3px);
            display: none;
        }
        @keyframes sacleAnimate {
            0% { opacity:0.5;transform: scale(1.3); }
            /* 50% { opacity:1 } */
            100% { opacity:0;transform: scale(2); }
        }
        .countryName{
            color: #fff;
            /* color: #fece61; */
            cursor: pointer;
            font-size: 18px;
            text-shadow: 3px 3px 1px black;
            pointer-events: all;
            /* margin-top: -15px; */
            filter: drop-shadow(1px 1px 1px black);
            text-align: center;
        }
        .countryName[code="yd"],.countryName[type="country"]{
            color: #fece61;
            text-align: center;
        }
        .demoBg{
            position: relative;
            left: calc(50% - 25px);
            /* margin-top: calc(-50% + 50px); */
            margin-top: calc(-50% + -20px);
        }
        .demoBg_title{
            color: #fff;
            cursor: pointer;
            font-size: 18px;
            text-shadow: 3px 3px 1px black;
            pointer-events: all;
            /* max-width: 200px; */
            text-align: center;
            background: url(img/demo_bg.png) no-repeat;
            background-size: 100% 100%;
            /* padding: 20px 15px; */
            margin-left: 15px;
            position: relative;
            top: 8px;
            max-width: 200px;
            min-width: 35px;
            padding: 10px 15px;
        }
        .demoBg_zz{
            /* background: url(img/demo_bg_zz.png) no-repeat; */
            background: url(img/xiangmu.png) no-repeat;
            background-size: 100% 100%;
            width: 25px;
            height: 25px;
        }
        .demoBg[type='yd'] .demoBg_title{
            background: url(img/demo_bg1.png) no-repeat;
            background-size: 100% 100%;
        }
        .demoBg[type='yd'] .demoBg_zz{
            /* background: url(img/demo_bg1_zz.png) no-repeat; */
            background: url(img/yingdi.png) no-repeat;
            background-size: 100% 100%;
        }
        .jdx{
            position: absolute;
            bottom: 50px;
            right: 50px;
            filter: drop-shadow(0px 0px 6px #2e72d7);
            display: none;
        }
        .xmydInfo_title{
            max-width: 500px;
            color: #fff;
            /* margin: 20px 0px; */
            font-size: 33px;
            /* font-weight: bold; */
            text-shadow: 2px 2px 1px #444;
            display: flex;
            cursor: pointer;
        }
        .xmydInfo_title[sel='ok']{
            background: url(img/xm/border.png) no-repeat;
            background-size: 100% 100%;
        }
        .xmydInfo_title[type="yd"]{
            color: #fece61;
        }
        .xmydInfo img{
            /* max-width: 500px; */
            pointer-events: none;
            /* max-height: 200px; */
            /* overflow: hidden; */
            width: 100%;
        }
        .xmydInfo{
            height: 430px;
            overflow: hidden;
            /* background: #6897ff61; */
            /* padding: 5px; */
            display: flex;
            width: 960px;
        }
        .xmydInfo_imgBg{
            overflow: auto;
            width: 500px;
            max-height: 300px;
            background: #000;
            margin: auto;
        }
        .xmydInfo_imgBg[isChange="true1"]{
            position: relative;
            left: 480px;
            margin: inherit;
            top: 100px;
            background: rgba(0, 0, 0, 0);
        }
        .xmydInfo_imgBg[isChange="true0"]{
            position: relative;
            right: 510px;
            margin: inherit;
            top: 100px;
            background: rgba(0, 0, 0, 0);
        }
        .xmydInfo_title_list{
            width: 400px;
            line-height: 85px;
            margin: 0px 30px;
            height: 425px;
            overflow: auto;
        }
        .xmydInfo_title img{
            width: 40px;
            height: 40px;
            margin-top: 20px;
            margin-right: 15px;
            margin-left: 15px;
        }
        .xmydInfo_title_list[isChange="true1"] img{
            position: relative;
            left: 400px;
            margin: 20px 0px 0px 0px;
        }
        .xmydInfo_title_list[isChange="true1"],.xmydInfo_title_list[isChange="true0"]{
            width: 600px;
        }
    </style>
</head>
<body>
    <img class="cir2" src="img/cir.png" />
    <img class="cir1" src="img/cir.png" />
    <img class="jdx" src="img/jdx.jpg"/>
    <div id="map"></div>
    <script>
        
    </script>
</body>
</html>