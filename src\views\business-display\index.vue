<template>
  <div class="business-display-container" :style="style">
    <template v-if="!$route.meta.hideParent">
      <!-- 简化的头部 -->
      <div class="simple-header">
        <!-- 左侧返回按钮 - 在国际国家详情模式下显示单个返回按钮 -->
        <div class="header-left" v-if="showCountryDetail && !isChina">
          <div class="back-button" @click="backToMain">
            <img src="@/assets/images/header/back.png" alt="返回" />
            <span>返回</span>
          </div>
        </div>
        
        <!-- 左侧双按钮 - 在中国境内任何层级下显示两个按钮 -->
        <div class="header-left-double" v-if="showDoubleButtons && ((isShow && !showCountryDetail) || (showCountryDetail && isChina))">
          <div class="back-button" @click="back" :class="{ 'disabled': isBackToHomeExecuting }">
            <img src="@/assets/images/header/back.png" alt="返回首页" />
            <span>返回首页</span>
          </div>
          <div class="back-button" @click="backToMain" :class="{ 'disabled': isBackToMainExecuting }">
            <img src="@/assets/images/header/back.png" alt="返回上一级" />
            <span>返回上一级</span>
          </div>
        </div>
        
        <!-- 中间标题 -->
        <div class="header-center">
          <div class="title-wrapper">
            <img src="@/assets/images/header/ywbj.png" alt="背景" class="title-bg" />
          </div>
          <!-- 国家名称显示区域 -->
          <div class="country-name-display" v-if="displayCountryName">
            <div class="country-name-text">{{ displayCountryName }}</div>
          </div>
        </div>
        
        <!-- 右上角控制区域 -->
        <div class="header-controls">
          <!-- 展开控制 - 在地球模式且未下钻时显示 -->
          <div class="control-item expand-collapse-item" v-if="!isExpanded && !isShow && !showCountryDetail" @click="toggleExpand">
            <div class="control-icon">
              <svg-icon name="展开" color="#ffffff" style="width: 32px; height: 32px;" />
            </div>
            <span class="control-text">展开</span>
          </div>
          <!-- 收起控制 - 在平面模式且未下钻时显示 -->
          <div class="control-item expand-collapse-item" v-if="isExpanded && !isShow && !showCountryDetail" @click="toggleExpand">
            <div class="control-icon">
              <svg-icon name="不展开" color="#ffffff" style="width: 32px; height: 32px;" />
            </div>
            <span class="control-text">收起</span>
          </div>
          <!-- 切换到现场视频的图标 - 在地球模式且未下钻时显示 -->
          <div class="control-item video-switch" @click="goToVideo" title="现场视频">
            <div class="control-icon">
              <img src="@/assets/images/header/视频.png" alt="现场视频" class="video-icon" />
            </div>
            <span class="control-text">视频</span>
          </div>
                  </div>
        </div>

        <!-- 主页面内容 - 业务布局总览 -->
      <div v-if="!showCountryDetail" class="project-container absolute top-0 left-0 pt-[120px] px-[20px] box-border w-full h-full bg-no-repeat bg-cover flex justify-between" 
           :class="{ 'expanded': isExpanded }">
        
        <!-- 左侧列表 - 境外数据（默认状态和中国点击状态都可能显示） -->
        <div class="leftList" v-if="!isShow">
          <div class="items" v-for="item in itemList" :key="item.id">
            <div class="item-img">
              <RotatingCircle style="width: 130px;height: 130px;"><svg-icon :name="item.svgName" color="#fff"
                  style="width: 48px;height: 48px;" /></RotatingCircle>
            </div>
            <div class="item-content">
              <div class="item-title">{{ item.name }}</div>
              <div class="item-amount">
                <span class="text-[36px] font-extrabold text-[#EEF4FF] mr-2 bgfont">
                  <count-to v-if="item.id === 3 || item.id === 4 || item.id === 5" :start-val="0" :end-val="item.amount"
                    :duration="2000" :decimals="0" :autoplay="true" separator="," />
                  <template v-else>{{ formattedNumber(item.amount) }}</template>
                </span><span class="bgfont text-[26px]">{{ item.unit }}</span>
              </div>
              <!-- Personnel breakdown for Overseas Personnel -->
              <div v-if="item.id == '6'" class="personnel-breakdown">
                <div class="breakdown-row">
                  <span class="breakdown-label">{{ $t('project.chinese') }}</span>
                  <span class="breakdown-value">{{ formattedNumber(foreignData?.中方 || 0) }}</span>
                  <span class="breakdown-unit">{{ $t('project.people') }}</span>
                </div>
                <div class="breakdown-row">
                  <span class="breakdown-label">{{ $t('project.foreign') }}</span>
                  <span class="breakdown-value">{{ formattedNumber(foreignData?.外籍 || 0) }}</span>
                  <span class="breakdown-unit">{{ $t('project.people') }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 右侧列表 - 境内数据（总是显示） -->
        <div class="rightList">
          <div class="items" v-for="item in itemListRight" :key="item.id">
            <div class="item-img">
              <RotatingCircle style="width: 100px;height: 100px;"><svg-icon :name="item.svgName" color="#fff"
                  style="width: 48px;height: 48px;" /></RotatingCircle>
            </div>
            <div class="item-content">
              <div class="item-title">{{ item.name }}</div>
              <div class="item-amount">
                <span class="text-[36px] font-extrabold text-[#EEF4FF] mr-2 bgfont">
                  <count-to v-if="item.id === 3 || item.id === 4 || item.id === 5" :start-val="0"
                    :end-val="parseAmount(item.amount)" :duration="2000" :decimals="0" :autoplay="true" separator="," />
                  <template v-else>{{ formattedNumber(item.amount) }}</template>
                </span><span class="bgfont text-[26px]">{{ item.unit }}</span>
              </div>
              <div></div>
            </div>
            <div class="absolute right-3 top-20">
              <div v-if="item.id == '6'">
                <span
                  style="background: linear-gradient(180deg, rgba(0,132,255,0) 0%, #0084FF 50%, rgba(0,132,255,0) 100%); border-radius: 0px 0px 0px 0px;margin-right: 6px;">{{
                    $t('project.formalEmployees') }}</span>
                <span class="bgfont" style="font-weight: bold;font-size: 22px;">{{
                  formattedNumber(innerData?.正式员工 || 0) }}</span>{{ $t('project.people') }}
              </div>
              <div v-if="item.id == '6'">
                <span
                  style="background: linear-gradient(180deg, rgba(0,132,255,0) 0%, #0084FF 50%, rgba(0,132,255,0) 100%); border-radius: 0px 0px 0px 0px;margin-right: 6px;">{{
                    $t('project.otherEmployment') }}</span>
                <span class="bgfont" style="font-weight: bold;font-size: 22px;">{{
                  formattedNumber(innerData?.其他形式用工 || 0) }}</span>{{ $t('project.people') }}
              </div>
            </div>
          </div>
        </div>


      </div>

      <!-- 国家详情内容 - 从country.vue整合过来 -->
      <div v-if="showCountryDetail" class="project-container absolute top-0 left-0 pt-[120px] px-[20px] box-border w-full h-full bg-no-repeat bg-cover flex justify-between"
           :class="{ 'expanded': isExpanded }">
        <div class="leftList" :class="{ 'leftList-china': isChina }">
          <div v-if="countryInfoLists.length === 0" class="items" v-for="(item, index) in countryInfoList" :key="index">
            <div class="item-img">
              <RotatingCircle style="width: 100px;height: 100px;"><svg-icon :name="item.svgName" color="#fff"
                  style="width: 50px;height: 50px;" /></RotatingCircle>
            </div>
            <div class="item-content">
              <div class="item-title">{{ item.name }}</div>
              <div class="item-amount">
                <span class="text-[30px] font-extrabold text-[#EEF4FF] mr-2 bgfont">
                  <count-to v-if="item.needCount" :start-val="0" :end-val="item.amount" :duration="2000" :decimals="2"
                    :autoplay="true" separator="," />
                  <template v-else>{{ item.amount }}</template>
                </span><span class="bgfont">{{ item.unit }}</span>
              </div>
            </div>
            <div class="absolute right-3 top-20" v-if="item.id === 'renshu'">
              <template v-if="isChina">
                <div>
                  <span class="bg-gradient">{{ $t('country.formalEmployees') }}</span>
                  <span class="text-[20px] font-extrabold text-[#EEF4FF] ml-5 mr-2 font-['xiaoweiLogo']">{{
                    zsyg }}</span><span>{{ $t('country.units.people') }}</span>
                </div>
                <div>
                  <span class="bg-gradient">{{ $t('country.otherEmploymentForms') }}</span>
                  <span class="text-[20px] font-extrabold text-[#EEF4FF] ml-5 mr-2 font-['xiaoweiLogo']">{{
                    (lwpq) + (qtyg) + (hzdw) }}</span><span>{{ $t('country.units.people') }}</span>
                </div>
              </template>
              <template v-else>
                <div>
                  <span class="bg-gradient">{{ $t('country.chinese') }}</span>
                  <span class="text-[20px] font-extrabold text-[#EEF4FF] ml-5 mr-2 font-['xiaoweiLogo']">{{
                    gnrs }}</span><span>{{ $t('country.units.people') }}</span>
                </div>
                <div>
                  <span class="bg-gradient">{{ $t('country.foreign') }}</span>
                  <span class="text-[20px] font-extrabold text-[#EEF4FF] ml-5 mr-2 font-['xiaoweiLogo']">{{
                    hwrs }}</span><span>{{ $t('country.units.people') }}</span>
                </div>
              </template>
            </div>
          </div>
          <div v-if="countryInfoLists.length > 0" class="items" v-for="(item, index) in countryInfoLists" :key="index"
               :class="{
                 'project-type-clickable': needProjectTypeInteraction && (item.id === 'construction-projects' || item.id === 'design-projects'),
                 'project-type-selected': needProjectTypeInteraction && (item.id === 'construction-projects' || item.id === 'design-projects') &&
                   ((item.id === 'construction-projects' && selectedProjectType === '施工') || 
                    (item.id === 'design-projects' && selectedProjectType === '设计'))
               }"
               @click="handleProjectItemClick(item)">
            <div class="item-img">
              <RotatingCircle style="width: 100px;height: 100px;"><svg-icon :name="item.svgName" color="#fff"
                  style="width: 50px;height: 50px;" /></RotatingCircle>
            </div>
            <div class="item-content">
              <div class="item-title">{{ item.name }}</div>
              <div class="item-amount">
                <span class="text-[30px] font-extrabold text-[#EEF4FF] mr-2 bgfont">
                  <count-to v-if="item.needCount" :start-val="0" :end-val="item.amount" :duration="2000" :decimals="2"
                    :autoplay="true" separator="," />
                  <template v-else>{{ item.amount }}</template>
                </span><span class="bgfont">{{ item.unit }}</span>
              </div>
            </div>
            <div class="absolute right-3 top-20" v-if="item.id === 'renshu'">
              <template v-if="isChina">
                <div>
                  <span class="bg-gradient">{{ $t('country.formalEmployees') }}</span>
                  <span class="text-[20px] font-extrabold text-[#EEF4FF] ml-5 mr-2 font-['xiaoweiLogo']">{{
                    zsyg }}</span><span>{{ $t('country.units.people') }}</span>
                </div>
                <div>
                  <span class="bg-gradient">{{ $t('country.otherEmploymentForms') }}</span>
                  <span class="text-[20px] font-extrabold text-[#EEF4FF] ml-5 mr-2 font-['xiaoweiLogo']">{{
                    (lwpq) + (qtyg) + (hzdw) }}</span><span>{{ $t('country.units.people') }}</span>
                </div>
              </template>
              <template v-else>
                <div>
                  <span class="bg-gradient">{{ $t('country.chinese') }}</span>
                  <span class="text-[20px] font-extrabold text-[#EEF4FF] ml-5 mr-2 font-['xiaoweiLogo']">{{
                    gnrs }}</span><span>{{ $t('country.units.people') }}</span>
                </div>
                <div>
                  <span class="bg-gradient">{{ $t('country.foreign') }}</span>
                  <span class="text-[20px] font-extrabold text-[#EEF4FF] ml-5 mr-2 font-['xiaoweiLogo']">{{
                    hwrs }}</span><span>{{ $t('country.units.people') }}</span>
                </div>
              </template>
            </div>
          </div>
        </div>

        <!-- 中部偏右的项目类型选择器（与左侧施工/设计联动，仅在国内可交互时显示） -->
        <div
          v-if="needProjectTypeInteraction"
          class="project-type-selector"
        >
          <ProjectTypeSelector
            v-model="selectedProjectType"
            :devices="projectTypeDevices"
            placeholder="选择项目类型"
            id-field="id"
            name-field="name"
            @change="onProjectTypeSelect"
          />
        </div>

        <!-- 右侧项目/营地列表 -->
        <!-- <div class="right-list-container" v-if="rightList.length > 0">
          <div class="list-content">
            <div
              v-for="(item, index) in rightList"
              :key="item.data.项目ID || item.data.营地id"
              class="list-item"
              @click="onRightListItemClick(item)"
              @mouseenter="handleItemHover(item)"
              @mouseleave="clearHighlight"
            >
              <span class="item-index">{{ index + 1 }}</span>
              <span class="item-name">{{ item.name }}</span>
            </div>
          </div>
        </div> -->
      </div>

    </template>
    <!-- 地球/地图显示 - 独立显示层 -->
    <div class="earth-map-container">
      <iframe :src="iframeSrc" id="iframe" class="w-full h-full iframe-container"></iframe>
    </div>
    <!-- 嵌套路由子视图，仅在详情时展示 -->
    <router-view v-if="$route.meta.hideParent" />
  </div>
</template>

<script setup>
import { CountTo } from 'vue3-count-to';
import RotatingCircle from '@/components/RotatingCircle.vue';
import { onMounted, ref, onUnmounted, computed, watch, onBeforeUnmount } from "vue"; 
import { useRouter, useRoute } from "vue-router";
import { useI18n } from 'vue-i18n';
import country from "@/assets/images/layout/country.png";
import project from "@/assets/images/layout/project.png";
import amount from "@/assets/images/layout/amount.png";
import produce from "@/assets/images/layout/produce.png";
import engineering from "@/assets/images/layout/engineering.png";
import people from "@/assets/images/layout/people.png";
import request from '@/utils/request';
import { useIndex } from '@/assets/js/windowScale.js';
import ProjectTypeSelector from '@/views/business-display/components/ProjectTypeSelector.vue';

const router = useRouter();
const route = useRoute();
const { t, locale } = useI18n();

// 大屏适配功能
const { style, setScale } = useIndex();

// 简化头部的状态
const isExpanded = ref(false);

// 页面状态管理
const iframe = ref(null);
const iframeSrc = ref("");
const isDev = import.meta.env && import.meta.env.DEV;
const showBack = ref(false);
const isShow = ref(false);
const showCountryDetail = ref(false); // 控制是否显示国家详情
const currentCountry = ref(sessionStorage.getItem('clickCountry') || ''); // 当前选中的国家名称
const displayCountryName = ref(''); // 页面标题下方显示的国家名称
const showDoubleButtons = ref(false); // 控制双按钮延时显示

// 原项目页面的数据
const foreignData = ref(null);
const innerData = ref(null);

// 国家详情页面的数据（从country.vue整合过来）
const rightList = ref([]);
const isChina = ref(false);
const countryData = ref(0);
const projectApiData = ref([]);
const campApiData = ref([]);
const gcksr = ref(0);
const htze = ref(0);
const bnmb = ref(0);
const renshu = ref(0);
const gnrs = ref(0);
const hwrs = ref(0);
const zsyg = ref(0);
const lwpq = ref(0);
const qtyg = ref(0);
const hzdw = ref(0);
const countryInfoLists = ref([]);
const lastBackTime = ref(0);
let throttleTimer = ref(null);
const isBackToMainExecuting = ref(false); // 防止重复执行的状态标识
const isBackToHomeExecuting = ref(false); // 防止返回首页重复执行的状态标识

// 项目类型选择状态管理
const selectedProjectType = ref('施工'); // 默认选中施工项目

// 响应式的当前级别
const currentLevel = ref(sessionStorage.getItem('countryLevel') || '');

// 监听存储变化，更新响应式变量
watch([isChina], () => {
  currentLevel.value = sessionStorage.getItem('countryLevel') || '';
  currentCountry.value = sessionStorage.getItem('clickCountry') || '';
  console.log('状态更新:', { 
    currentLevel: currentLevel.value, 
    currentCountry: currentCountry.value, 
    isChina: isChina.value 
  });
});

// 判断是否需要项目类型交互
const needProjectTypeInteraction = computed(() => {
  console.log('计算交互状态:', { 
    currentLevel: currentLevel.value, 
    currentCountry: currentCountry.value, 
    isChina: isChina.value 
  });
  
  if (!isChina.value) {
    console.log('不是中国，返回false');
    return false; // 不是中国
  }
  
  // 江苏省的具体城市（市级）可以点击
  const jiangsuCities = ['南京', '苏州', '无锡', '常州', '徐州', '扬州', '镇江', '泰州', '淮安', '盐城', '连云港', '宿迁'];
  if (jiangsuCities.some(city => currentCountry.value.includes(city))) {
    console.log('检测到江苏省城市:', currentCountry.value, '级别:', currentLevel.value);
    return currentLevel.value === 'city';
  }
  
  // 江苏省省级不可点击
  if (currentCountry.value === '江苏省' || currentCountry.value.includes('江苏省')) {
    console.log('检测到江苏省省级，不可点击:', currentCountry.value);
    return false;
  }
  
  // 其他省份的省级可以点击
  if (currentLevel.value === 'province') {
    console.log('其他省份省级可点击:', currentCountry.value);
    return true;
  }
  
  console.log('默认返回false');
  return false;
});

// 项目类型点击处理函数
const handleProjectTypeClick = (type) => {
  if (!needProjectTypeInteraction.value) return;
  
  selectedProjectType.value = type;
  
  // 发送消息给iframe
  if (iframe.value && iframe.value.contentWindow) {
    iframe.value.contentWindow.postMessage({
      eve: 'changeDemoInfoByProjectType',
      type: type
    }, '*');
  }
};

// 处理项目条目点击
const handleProjectItemClick = (item) => {
  // 只有施工项目数量和设计项目数量才能点击
  if (!needProjectTypeInteraction.value) return;
  
  if (item.id === 'construction-projects') {
    handleProjectTypeClick('施工');
  } else if (item.id === 'design-projects') {
    handleProjectTypeClick('设计');
  }
};

// 下拉选择器的项目类型选项（与左侧保持一致）
const projectTypeDevices = computed(() => [
  { id: '施工', name: '施工项目数量', status: '1' },
  { id: '设计', name: '设计项目数量', status: '1' }
]);

// 选择器变更时触发与左侧一致的切换逻辑
const onProjectTypeSelect = (option) => {
  if (!option || !option.id) return;
  handleProjectTypeClick(option.id);
};

// 展开/收起功能
const toggleExpand = () => {
  // 确保iframe存在
  if (!iframe.value) {
    iframe.value = document.getElementById("iframe");
    if (!iframe.value) return;
  }

  // 根据当前状态切换展开模式
  if (isExpanded.value === false) {
    isExpanded.value = true;
    // 保存展开状态到 sessionStorage
    sessionStorage.setItem('isExpanded', 'true');
    iframe.value.contentWindow.postMessage(
      { eve: "changeModel", data: "plan" },
      "*"
    );
  } else {
    isExpanded.value = false;
    // 保存收起状态到 sessionStorage
    sessionStorage.setItem('isExpanded', 'false');
    iframe.value.contentWindow.postMessage(
      { eve: "changeModel", data: "earth" },
      "*"
    );
  }
};

/**
 * 重置到初始状态的统一方法
 * 确保从任何状态都能正确返回到页面的初始状态
 * @param {boolean} resetExpandState - 是否重置展开状态，默认为 false（保持展开状态）
 */
const resetToInitialState = (resetExpandState = false) => {
  console.log('重置到业务展示页面初始状态', resetExpandState ? '(包括展开状态)' : '(保持展开状态)');

  // 基础状态重置
  if (resetExpandState) {
    isExpanded.value = false;        // 地球模式（非展开）
    sessionStorage.removeItem('isExpanded'); // 清除保存的展开状态
  }
  showBack.value = false;          // 隐藏返回按钮
  isShow.value = false;            // 隐藏境内数据模式
  showCountryDetail.value = false; // 隐藏国家详情
  currentCountry.value = '';       // 清空当前国家
  displayCountryName.value = '';   // 清空显示的国家名称
  showDoubleButtons.value = false; // 隐藏双按钮
  isBackToMainExecuting.value = false; // 重置执行状态
  isBackToHomeExecuting.value = false; // 重置返回首页执行状态
  
  // 国家详情相关状态重置
  rightList.value = [];            // 清空右侧项目/营地列表
  isChina.value = false;           // 重置中国模式标识
  countryInfoLists.value = [];     // 清空国家信息列表
  
  // 数据状态重置
  gcksr.value = 0;
  htze.value = 0;
  bnmb.value = 0;
  renshu.value = 0;
  gnrs.value = 0;
  hwrs.value = 0;
  zsyg.value = 0;
  lwpq.value = 0;
  qtyg.value = 0;
  hzdw.value = 0;
  
  // 重置项目类型选择为默认的施工状态
  selectedProjectType.value = '施工';
  
  // 清理存储状态
  localStorage.removeItem("isChina");
  sessionStorage.removeItem("clickCountry");
  sessionStorage.removeItem("countryTitle");
  sessionStorage.removeItem("countryLevel");
  sessionStorage.removeItem("clickProject");
  
  // 向iframe发送重置消息
  // setTimeout(() => {
  //   if (iframe.value && iframe.value.contentWindow) {
  //     iframe.value.contentWindow.postMessage(
  //       { eve: "cancle" },
  //       "*"
  //     );
  //   }
  // }, 100);
  
  console.log('初始状态重置完成');
};

/**
 * 处理从项目详情返回到国家/地区详情页面
 * 根据保存的城市或省份信息，重新请求对应数据
 */
 const handleBackToCountryDetail = async () => {
  console.log('处理从项目详情返回到国家/地区详情');
  
  // 获取存储的国家/地区信息
  const savedCountry = sessionStorage.getItem("clickCountry");
  const savedLevel = sessionStorage.getItem("countryLevel");
  const savedTitle = sessionStorage.getItem("countryTitle");
  
  console.log('恢复状态信息:', {
    savedCountry,
    savedLevel,
    savedTitle,
    isChina: localStorage.getItem("isChina")
  });
  
  if (savedCountry && savedLevel) {
    // 恢复到国家/地区详情状态
    showCountryDetail.value = true;
    currentCountry.value = savedCountry;
    const domestic = localStorage.getItem("isChina") === "1";
    isChina.value = domestic;
    
    // 恢复显示的国家名称 - 点到哪里就显示哪里
    if (savedCountry) {
      displayCountryName.value = savedCountry;
    }
    
    console.log(`开始恢复到${domestic ? '国内' : '国际'}${savedLevel}详情：${savedCountry}`);
    
    // 如果是中国境内的省市区，延时2秒显示双按钮
    if (domestic) {
      setTimeout(() => { 
        showDoubleButtons.value = true; 
      }, 2000);
    }
    
    try {
      // 重新获取国家/地区详情数据 - 根据不同层级调用不同API
      await getInfoByLevel(savedLevel, savedCountry);
      
      // 重新获取右侧项目/营地列表
      await fetchRightList(savedCountry);
      
      // 根据层级重新获取对应的基础数据
      if (domestic) {
        // 如果是国内层级，重新获取境内数据
        await getInnerInfo();
        console.log(`国内${savedLevel}数据刷新完成：${savedCountry}`);
      } else {
        // 如果是国际层级，重新获取境外数据
        await getForeignInfo();
        console.log(`国际${savedLevel}数据刷新完成：${savedCountry}`);
      }
      
      // 不再自动触发项目类型选择，保持状态但不发送消息给模型
      
      console.log(`成功恢复到${domestic ? '国内' : '国际'}${savedLevel}详情：${savedCountry}`);
    } catch (error) {
      console.error('恢复详情数据失败:', error);
      // 如果数据加载失败，回退到安全状态
      resetToInitialState();
    }
  } else {
    // 如果没有保存的信息，退回到初始状态
    console.log('没有找到保存的国家/地区信息，退回到初始状态');
    resetToInitialState();
    // 重新获取首页数据
    await Promise.all([getForeignInfo(), getInnerInfo()]);
  }
  
  // 清理返回类型标记
  sessionStorage.removeItem("returnType");
};

/**
 * 改进的 getInfoByLevel 函数，增加更详细的日志和错误处理
 */
const getInfoByLevel = async (level, name) => {
  try {
    console.log(`开始获取${level}级别数据:`, name);
    let response;
    let apiEndpoint = '';
    
    // 根据层级和是否为国内选择对应的API
    if (level === 'country' && localStorage.getItem('isChina') !== '1') {
      // 国际国家级数据
      apiEndpoint = '/globalManage/zjmanage/largescreen/getForeignInfoByCountry';
      response = await request.get(apiEndpoint, { params: { gb: name } });
    } else if (level === 'province') {
      // 国内省级
      apiEndpoint = '/globalManage/zjmanage/largescreen/getInnerProviceCityInfo';
      response = await request.get(apiEndpoint, { params: { province: name } });
    } else if (level === 'city') {
      // 国内市级
      apiEndpoint = '/globalManage/zjmanage/largescreen/getInnerProviceCityDistrictInfo';
      response = await request.get(apiEndpoint, { params: { city: name } });
    } else if (level === 'district') {
      // 国内区县级
      apiEndpoint = '/globalManage/zjmanage/largescreen/getInnerDistrictBaseInfo';
      response = await request.get(apiEndpoint, { params: { district: name } });
    } else {
      // 默认通用接口
      apiEndpoint = '/globalManage/zjmanage/largescreen/getInfoByLevel';
      response = await request.get(apiEndpoint, { params: { level, name } });
    }
    
    console.log(`API请求完成 ${apiEndpoint}:`, response);
    
    // 数据映射
    if (response.code === 0 && response.data.length > 0) {
      const data = response.data[0];
      console.log('获取到数据:', data);
      
      // 基础指标赋值
      htze.value = safeNumber(data.合同总额 || data.在建项目合同总额);
      bnmb.value = safeNumber(data.累计产值 || data.本年目标);
      gcksr.value = safeNumber(data.工程款收入);
      renshu.value = safeNumber(data.项目总人数);
      
      // 人员明细赋值
      if (level === 'country' && localStorage.getItem('isChina') !== '1') {
        // 国际模式下的人员分类
        gnrs.value = safeNumber(data.中方);
        hwrs.value = safeNumber(data.外籍);
        console.log('国际人员数据:', { gnrs: gnrs.value, hwrs: hwrs.value });
      } else {
        // 国内模式下的人员分类
        zsyg.value = safeNumber(data.正式员工);
        lwpq.value = safeNumber(data.劳务派遣);
        qtyg.value = safeNumber(data.其他用工);
        hzdw.value = safeNumber(data.合作单位);
        console.log('国内人员数据:', { 
          zsyg: zsyg.value, 
          lwpq: lwpq.value, 
          qtyg: qtyg.value, 
          hzdw: hzdw.value 
        });
      }
      
      // 构建信息列表
      countryInfoLists.value = [
        // 中国境内：显示施工项目数量和设计项目数量分别统计
        ...(isChina.value ? [
          { 
            id: 'construction-projects', 
            name: '施工项目数量', 
            amount: safeNumber(data.施工项目总数 || 0), 
            unit: t('country.units.projects'), 
            svgName: 'home-2', 
            needCount: false 
          },
          { 
            id: 'design-projects', 
            name: '设计项目数量', 
            amount: safeNumber(data.设计项目总数 || 0), 
            unit: t('country.units.projects'), 
            svgName: 'home-2', 
            needCount: false 
          }
        ] : [
          // 国外：保持原有的在建项目总数显示
          { 
            id: 'projects', 
            name: t('country.projectsUnderConstruction'), 
            amount: safeNumber(data.在建项目数量 || data.境外在建项目 || data.在建项目总数), 
            unit: t('country.units.projects'), 
            svgName: 'home-2', 
            needCount: false 
          }
        ]),
        // 只在国际国家（非中国）时添加营地数量
        ...(isChina.value ? [] : [{ 
          id: 'camps', 
          name: t('country.campCount'), 
          amount: safeNumber(data.营地数量 || 0), 
          unit: t('country.units.camps'), 
          svgName: 'home-1', 
          needCount: false 
        }]),
        { 
          id: 'htze', 
          name: t('country.contractAmount'), 
          amount: htze.value, 
          unit: isChina.value ? t('country.units.tenThousandYuan') : t('country.units.tenThousandUSD'), 
          svgName: 'home-3-' + (isChina.value ? 'r' : 'l'), 
          needCount: true 
        },
        { 
          id: 'bnmb', 
          name: t('country.cumulativeOutput'), 
          amount: bnmb.value, 
          unit: isChina.value ? t('country.units.tenThousandYuan') : t('country.units.tenThousandUSD'), 
          svgName: 'home-4-' + (isChina.value ? 'r' : 'l'), 
          needCount: true 
        },
        { 
          id: 'gcksr', 
          name: t('country.engineeringRevenue'), 
          amount: gcksr.value, 
          unit: isChina.value ? t('country.units.tenThousandYuan') : t('country.units.tenThousandUSD'), 
          svgName: 'home-5-' + (isChina.value ? 'r' : 'l'), 
          needCount: true 
        }
      ];
      
      console.log(`${level}级别数据加载完成:`, {
        isChina: isChina.value,
        ...(isChina.value ? {
          constructionProjects: safeNumber(data.施工项目总数 || 0),
          designProjects: safeNumber(data.设计项目总数 || 0)
        } : {
          projects: safeNumber(data.在建项目数量 || data.境外在建项目 || data.在建项目总数),
          camps: safeNumber(data.营地数量 || 0)
        }),
        htze: htze.value,
        bnmb: bnmb.value,
        gcksr: gcksr.value,
        renshu: renshu.value
      });
    } else {
      console.warn(`${level}级别数据为空或格式异常:`, response);
      // 如果数据为空，至少初始化一个基础的列表结构
      countryInfoLists.value = [
        // 中国境内：显示施工项目数量和设计项目数量分别统计
        ...(isChina.value ? [
          { id: 'construction-projects', name: '施工项目数量', amount: 0, unit: t('country.units.projects'), svgName: 'home-2', needCount: false },
          { id: 'design-projects', name: '设计项目数量', amount: 0, unit: t('country.units.projects'), svgName: 'home-2', needCount: false }
        ] : [
          // 国外：保持原有的在建项目总数显示
          { id: 'projects', name: t('country.projectsUnderConstruction'), amount: 0, unit: t('country.units.projects'), svgName: 'home-2', needCount: false }
        ]),
        // 只在国际国家（非中国）时添加营地数量
        ...(isChina.value ? [] : [{ 
          id: 'camps', 
          name: t('country.campCount'), 
          amount: 0, 
          unit: t('country.units.camps'), 
          svgName: 'home-1', 
          needCount: false 
        }]),
        { id: 'htze', name: t('country.contractAmount'), amount: 0, unit: isChina.value ? t('country.units.tenThousandYuan') : t('country.units.tenThousandUSD'), svgName: 'home-3-' + (isChina.value ? 'r' : 'l'), needCount: true },
        { id: 'bnmb', name: t('country.cumulativeOutput'), amount: 0, unit: isChina.value ? t('country.units.tenThousandYuan') : t('country.units.tenThousandUSD'), svgName: 'home-4-' + (isChina.value ? 'r' : 'l'), needCount: true },
        { id: 'gcksr', name: t('country.engineeringRevenue'), amount: 0, unit: isChina.value ? t('country.units.tenThousandYuan') : t('country.units.tenThousandUSD'), svgName: 'home-5-' + (isChina.value ? 'r' : 'l'), needCount: true }
      ];
    }
  } catch (error) {
    console.error(`获取${level}级别数据失败:`, error);
    // 抛出错误，让调用方处理
    throw error;
  }
};

/**
 * 改进的右侧列表获取函数，增加错误处理
 */
const fetchRightList = async (areaName) => {
  try {
    console.log('开始获取项目/营地列表:', areaName);
    
    const currentLevel = sessionStorage.getItem('countryLevel');
    const requests = [];
    
    // 根据区域类型和层级构建请求
    requests.push(
      request.get('/globalManage/zjmanage/largescreen/getXmxxV2', { params: { gb: areaName } }),
      request.get('/globalManage/zjmanage/largescreen/getYdxxV2', { params: { gb: areaName } })
    );
    
    // 如果是区县级，额外调用区县项目接口
    if (currentLevel === 'district') {
      requests.push(
        request.get('/globalManage/zjmanage/largescreen/getXmjbxx', { params: { district: areaName } })
      );
    }
    
    const [projectRes, campRes, projectRes2] = await Promise.all(requests);
    
    // 处理项目数据
    const projects = projectRes?.code === 0 && projectRes.data
      ? projectRes.data.map(p => ({ 
          name: p.项目名称, 
          data: p, 
          type: 'project' 
        }))
      : [];
    
    // 处理营地数据
    const camps = campRes?.code === 0 && campRes.data
      ? campRes.data.map(c => ({ 
          name: c.营地名称, 
          data: c, 
          type: 'camp' 
        }))
      : [];

      console.log('营地数据:', camps);
    
    // 处理区县级项目数据
    const projects2 = projectRes2?.code === 0 && projectRes2.data
      ? projectRes2.data.map(p => ({ 
          name: p.项目名称, 
          data: p, 
          type: 'project' 
        }))
      : [];
    
    rightList.value = [...projects, ...camps, ...projects2];
    
    console.log(`${areaName}项目/营地列表获取完成，共${rightList.value.length}项:`, {
      projects: projects.length,
      camps: camps.length,
      projects2: projects2.length
    });
  } catch (error) {
    console.error('获取项目/营地列表失败:', error);
    rightList.value = [];
  }
};

/**
 * 处理从视频页面返回的逻辑
 */
const handleBackFromVideo = async () => {
  console.log('处理从视频页面返回，恢复保存的状态');
  
  // 获取从视频页面保存的状态
  const savedState = sessionStorage.getItem("beforeVideoState");
  
  if (savedState) {
    try {
      const state = JSON.parse(savedState);
      console.log('恢复视频页面前的状态:', state);
      
      // 恢复页面状态
      isExpanded.value = state.isExpanded || false;
      // 同步展开状态到 sessionStorage
      sessionStorage.setItem('isExpanded', state.isExpanded ? 'true' : 'false');
      showBack.value = state.showBack || false;
      isShow.value = state.isShow || false;
      showCountryDetail.value = state.showCountryDetail || false;
      currentCountry.value = state.currentCountry || '';
      
      // 恢复显示的国家名称 - 点到哪里就显示哪里
      if (state.displayCountryName) {
        displayCountryName.value = state.displayCountryName;
      } else if (state.currentCountry) {
        // 如果没有保存的displayCountryName，直接显示当前国家/地区名称
        displayCountryName.value = state.currentCountry;
      }
      
      // 恢复存储状态
      if (state.isChina) {
        localStorage.setItem("isChina", state.isChina);
        isChina.value = state.isChina === "1";
        
        // 如果是中国相关状态，延时2秒显示双按钮
        if (state.isChina === "1" && (state.isShow || state.showCountryDetail)) {
          setTimeout(() => { 
            showDoubleButtons.value = true; 
          }, 2000);
        }
      }
      if (state.clickCountry) {
        sessionStorage.setItem("clickCountry", state.clickCountry);
      }
      if (state.countryLevel) {
        sessionStorage.setItem("countryLevel", state.countryLevel);
      }
      if (state.countryTitle) {
        sessionStorage.setItem("countryTitle", state.countryTitle);
      }
      
      // 如果之前在国家详情状态，需要重新加载数据
      if (state.showCountryDetail && state.currentCountry) {
        const savedLevel = state.countryLevel;
        await getInfoByLevel(savedLevel, state.currentCountry);
        await fetchRightList(state.currentCountry);
        
        // 不再自动触发项目类型选择，保持状态但不发送消息给模型
      }
      
      console.log('视频页面状态恢复完成');
      
    } catch (error) {
      console.error('恢复视频页面状态失败:', error);
      // 如果恢复失败，回到初始状态
      await handleBackToHome();
    }
    
    // 清理保存的状态
    sessionStorage.removeItem("beforeVideoState");
  } else {
    console.log('未找到保存的视频页面状态，回到初始状态');
    await handleBackToHome();
  }
  
  // 重新获取数据
  await Promise.all([getForeignInfo(), getInnerInfo()]);
};

// 跳转到现场视频页面
const goToVideo = () => {
  console.log('跳转到现场视频子页面');
  
  // 保存当前状态，供视频页面返回时恢复
  const currentState = {
    isExpanded: isExpanded.value,
    showBack: showBack.value,
    isShow: isShow.value,
    showCountryDetail: showCountryDetail.value,
    currentCountry: currentCountry.value,
    displayCountryName: displayCountryName.value,
    isChina: localStorage.getItem("isChina"),
    clickCountry: sessionStorage.getItem("clickCountry"),
    countryLevel: sessionStorage.getItem("countryLevel"),
    countryTitle: sessionStorage.getItem("countryTitle")
  };
  
  sessionStorage.setItem('beforeVideoState', JSON.stringify(currentState));
  
  // 跳转到子路由
  router.push('/business-display/video-display');
};


// 返回到主页面
const backToMain = () => {
  // 如果正在执行中，直接返回，避免重复执行
  if (isBackToMainExecuting.value) {
    console.log('返回上一级操作正在执行中，忽略重复点击');
    return;
  }

  const now = Date.now();
  if (now - lastBackTime.value < 1500) { // 增加防抖间隔到1.5秒
    console.log('返回上一级操作过于频繁，请稍后再试');
    return;
  }
  
  lastBackTime.value = now;

  // 清除之前的定时器
  if (throttleTimer.value) {
    clearTimeout(throttleTimer.value);
  }

  // 设置执行状态，防止重复执行
  isBackToMainExecuting.value = true;

  throttleTimer.value = setTimeout(() => {
    try {
      // 检查当前是否在中国模式和当前的层级
      const currentIsChina = localStorage.getItem("isChina") === "1";
      const savedCountry = sessionStorage.getItem("clickCountry");
      const savedLevel = sessionStorage.getItem("countryLevel");
      
      if (currentIsChina) {
        // 如果当前在中国顶层（country级别），直接返回首页
        if (savedCountry === "中国" && savedLevel === "country") {
          console.log('从中国顶层返回到首页');
          if (iframe.value) {
            iframe.value.contentWindow.postMessage(
              { eve: "cancle" },
              "*"
            );
          }
          resetToInitialState();
          return; // 直接返回，避免重复发送消息
        } else {
          // 从中国境内省市区详情返回到中国境内数据模式
          console.log('从中国境内省市区详情返回到中国境内数据模式');
          showCountryDetail.value = false;
          isShow.value = true;
          showBack.value = true;
          rightList.value = [];
          currentCountry.value = '';
          displayCountryName.value = "中国"; // 返回显示中国
          
          // 延时2秒显示双按钮
          setTimeout(() => { 
            showDoubleButtons.value = true; 
          }, 2000);
          
          // 保持中国模式状态，只清理区域相关的session存储
          sessionStorage.removeItem("clickCountry");
          sessionStorage.setItem("countryTitle", "中国");
          
          // 重置项目类型选择为施工状态
          selectedProjectType.value = '施工';
        }
      } else {
        // 从国际国家详情返回到初始状态
        console.log('从国际国家详情返回到初始状态');
        if (iframe.value) {
          iframe.value.contentWindow.postMessage(
            { eve: "cancle" },
            "*"
          );
        }
        resetToInitialState();
        
        // 确保重置后的项目类型状态生效
        setTimeout(() => {
          console.log('返回首页后重置项目类型为施工状态');
        }, 500);
        
        return; // 直接返回，避免重复发送消息
      }
      
      // 统一向iframe发送返回消息，保持原有通信协议
      if (iframe.value) {
        iframe.value.contentWindow.postMessage(
          { eve: "cancle" },
          "*"
        );
      }
    } catch (error) {
      console.error('返回上一级操作执行失败:', error);
    } finally {
      // 无论成功失败都要重置执行状态，延时重置避免过快再次点击
      setTimeout(() => {
        isBackToMainExecuting.value = false;
      }, 500);
    }
  }, 200); // 增加延时到200ms，确保用户操作完成
};

// 返回到地球模式（中国境内数据模式的返回）
const back = async () => {
  // 如果正在执行中，直接返回，避免重复执行
  if (isBackToHomeExecuting.value) {
    console.log('返回首页操作正在执行中，忽略重复点击');
    return;
  }

  console.log('从境内数据返回到初始状态');
  
  // 设置执行状态，防止重复执行
  isBackToHomeExecuting.value = true;
  
  try {
    // 使用统一的重置方法
    resetToInitialState();
    
    // 确保iframe切换到地球模式并重新获取数据
    setTimeout(() => {
      if (iframe.value && iframe.value.contentWindow) {
        console.log('向iframe发送地球模式切换消息');
        // 发送切换到地球模式的消息
        iframe.value.contentWindow.postMessage(
          { eve: "changeModel", data: "earth" },
          "*"
        );
        // 发送取消消息，确保地图状态重置
        // iframe.value.contentWindow.postMessage(
        //   { eve: "cancle" },
        //   "*"
        // );
        // 发送token
        // iframe.value.contentWindow.postMessage(
        //   {
        //     type: "token",
        //     data: sessionStorage.getItem('token'),
        //   },
        //   "*"
        // );
      }
    }, 200);
    
    // 重新获取首页数据
    await getForeignInfo();
    await getInnerInfo();
    
    // 确保重置后的项目类型状态生效
    setTimeout(() => {
      console.log('返回首页后重置项目类型为施工状态');
    }, 500);
    
    console.log('境内返回首页状态重置完成');
  } catch (error) {
    console.error('返回首页操作执行失败:', error);
  } finally {
    // 延时重置执行状态，避免过快再次点击
    setTimeout(() => {
      isBackToHomeExecuting.value = false;
    }, 1000);
  }
};

// 境外数据列表 - 计算属性
const itemList = computed(() => {
  if (!foreignData.value) return [];
  return [
    {
      id: 1,
      src: country,
      name: t('project.dataFields.overseasBusinessCountries'),
      amount: Math.max(0, Number(foreignData.value.境外业务布局国家 || 0) - 1),
      unit: t('project.units.project'),
      svgName: "home-1"
    },
    {
      id: 2,
      src: project,
      name: t('project.dataFields.overseasProjects'),
      amount: Number(foreignData.value.境外在建项目 || 0),  
      unit: t('project.units.project'),
      svgName: "home-2"
    },
    {
      id: 3,
      src: amount,
      name: t('project.dataFields.contractAmount'),
      amount: parseAmount(foreignData.value.在建项目合同总额 || foreignData.value.合同总额 || 0),
      unit: t('project.units.hundredMillionUSD'),
      svgName: "home-3-l"
    },
    {
      id: 6,
      src: people,
      name: t('project.dataFields.overseasPersonnel'),
      amount: Number(foreignData.value.项目总人数 || 0),
      unit: t('project.units.person'),
      svgName: "home-6"
    }
  ];
});

// 境内数据列表 - 计算属性
const itemListRight = computed(() => {
  if (!innerData.value) return [];
  return [
    {
      id: 1,
      src: country,
      name: t('project.dataFields.domesticBusinessProvinces'),
      amount: Number(innerData.value.境内业务省份 || innerData.value.在建项目总数 || 0),
      unit: t('project.units.project'),
      svgName: "home-1"
    },
    {
      id: 2,
      src: project,
      name: t('project.dataFields.domesticProjectsWithDesign'),
      amount: Number(innerData.value.国内在建项目 || innerData.value.在建项目总数 || 0),
      unit: t('project.units.project'),
      svgName: "home-2"
    },
    {
      id: 3,
      src: amount,
      name: t('project.dataFields.contractAmount'),
      amount: parseAmount(innerData.value.在建项目合同总额 || innerData.value.合同总额 || 0),
      unit: t('project.units.hundredMillionYuan'),
      svgName: "home-3-r"
    },
    {
      id: 4,
      src: produce,
      name: t('project.dataFields.currentYearCollectionRate'),
      amount: innerData.value?.本年回款率 ? (Number(innerData.value.本年回款率) * 100).toFixed(2) : '0.00',
      unit: '%',
      svgName: "home-4-r"
    }
  ];
});

const formattedNumber = (value) => {
  if (typeof value == "number") {
    return value.toLocaleString();
  } else {
    return value;
  }
};

const parseAmount = (value) => {
  if (typeof value === 'string') {
    return Number(value.replace(/,/g, ''));
  }
  return value;
};

// 安全数字转换函数，防止NaN
const safeNumber = (val) => {
  const n = Number(val);
  return isNaN(n) ? 0 : n;
};

// 国家详情信息列表 - 计算属性
const countryInfoList = computed(() => [
  { id: "projects", name: t('country.projectsUnderConstruction'), amount: 0, unit: t('country.units.projects'), svgName: "home-2", needCount: false },
  { id: "htze", name: t('country.contractAmount'), amount: 0, unit: "", svgName: "", needCount: true },
  { id: "bnmb", name: t('country.cumulativeOutput'), amount: 0, unit: "", svgName: "", needCount: true },
  { id: "gcksr", name: t('country.engineeringRevenue'), amount: 0, unit: "", svgName: "", needCount: true },
  { id: "renshu", name: t('country.overseasPersonnel'), amount: 0, unit: t('country.units.people'), svgName: "home-6", needCount: false }
]);

// 处理右侧项目/营地列表点击
const onRightListItemClick = (item) => {
  // 优先使用 API 返回的 ID 字段，再 fallback 到项目ID或营地id
  const code = item.data.ID || item.data.项目ID || item.data.营地id;
  const type = (item.data.ID || item.data.项目ID) ? 'project' : 'yd';
  const payload = {
    eve: 'clickDemo',
    type,
    code,
    name: item.name,
    isGw: String(!isChina.value)
  };
  sessionStorage.setItem('clickProject', JSON.stringify(payload));
  // 根据点击类型跳转到对应详情页面
  const targetRoute = payload.type === 'project'
    ? '/business-display/project-detail'
    : '/business-display/yingdi';
  router.push(targetRoute);
};

// 处理项目/营地悬停与移出
let hoverTimer = null;
const handleItemHover = (item) => {
  if (hoverTimer) clearTimeout(hoverTimer);
  hoverTimer = setTimeout(() => {
    if (iframe.value && iframe.value.contentWindow) {
      iframe.value.contentWindow.postMessage(
        { eve: 'highLigtDemo', name: item.name },
        '*'
      );
    }
  }, 300);
};
const clearHighlight = () => {
  if (hoverTimer) clearTimeout(hoverTimer);
  if (iframe.value && iframe.value.contentWindow) {
    iframe.value.contentWindow.postMessage(
      { eve: 'highLigtDemo', name: '' },
      '*'
    );
  }
};

const getForeignInfo = async () => {
  try {
    const response = await request.get('/globalManage/zjmanage/largescreen/getForeignInfo');
    if (response.code === 0 && response.data.length > 0) {
      foreignData.value = response.data[0];
      console.log('境外数据', foreignData.value)
    }
  } catch (error) {
    console.error('获取境外数据失败:', error);
  }
};

const getInnerInfo = async () => {
  try {
    const response = await request.get('/globalManage/zjmanage/largescreen/getInnerInfo', {
      params: { gb: '中国' }
    });
    console.log('getInnerInfo API响应:', response);
    if (response.code === 0 && response.data.length > 0) {
      innerData.value = response.data[0];
      console.log('境内数据innerData赋值成功:', innerData.value);
      console.log('合同总额字段值:', innerData.value.合同总额);
      console.log('在建项目合同总额字段值:', innerData.value.在建项目合同总额);
    } else {
      console.log('getInnerInfo API返回数据为空或异常:', response);
    }
  } catch (error) {
    console.error('获取境内数据失败:', error);
  }
};

// 处理从iframe发送的消息
const handleMessage = async (e) => {
  if (isDev) console.log('business-display 收到消息:', e.data);
  
  // 发送token
  if (iframe.value) {
    iframe.value.contentWindow.postMessage(
      {
        type: "token",
        data: sessionStorage.getItem('token'),
      },
      "*"
    );
  }
  
  // 处理地图点击或初始化事件
  if (e.data.eve === "initDemoInfo" || e.data.eve === "clickTitle") {
    const regionName = e.data.title;
    const regionLevel = e.data.level || "country";
    // 切换到地球模式
    if (regionName === "earth") {
      console.log('切换到地球模式');
      showBack.value = false;
      isShow.value = false;
      showCountryDetail.value = false;
      displayCountryName.value = ''; // 清空显示的国家名称
      localStorage.removeItem("isChina");
      sessionStorage.removeItem("clickCountry");
      return;
    }
    // 中国顶层
    if (regionName === "中国" && regionLevel === "country") {
      console.log('点击中国，切换到境内数据');
      setTimeout(() => { 
        showBack.value = true; 
        showDoubleButtons.value = true; // 延时2秒显示双按钮
      }, 2000);
      isShow.value = true;
      displayCountryName.value = "中国"; // 显示中国
      localStorage.setItem("isChina", "1");
      sessionStorage.setItem("clickCountry", "中国");
      sessionStorage.setItem("countryLevel", "country");
      sessionStorage.setItem("countryTitle", "中国");
      return;
    }
    // 其他区域（包括国外国家及国内省市区）
    if (regionName && regionName !== "中国") {
      console.log('切换到区域详情:', regionLevel, regionName);
      sessionStorage.setItem("clickCountry", regionName);
      sessionStorage.setItem('countryLevel', regionLevel);
      currentCountry.value = regionName;
      currentLevel.value = regionLevel;
      console.log('更新后的状态:', { 
        currentCountry: currentCountry.value, 
        currentLevel: currentLevel.value,
        isChina: isChina.value 
      });
      
      // 判断是否为中国境内的省市区
      const isDomesticRegion = localStorage.getItem("isChina") === "1" || 
                              sessionStorage.getItem("countryTitle") === "中国";
      
      isChina.value = isDomesticRegion;
      localStorage.setItem("isChina", isDomesticRegion ? "1" : "0");
      showCountryDetail.value = true;
      
      // 设置显示的国家/地区名称 - 点到哪里就显示哪里
      displayCountryName.value = regionName;
      
      if (isDomesticRegion) {
        setTimeout(() => { 
          showDoubleButtons.value = true; 
        }, 2000);
      }
      
      // 加载详情数据与右侧列表
      await getInfoByLevel(regionLevel, regionName);
      await fetchRightList(regionName);
      
      // 不再自动触发项目类型选择，只有点击时才发送消息
      // 保持默认的施工项目状态显示，但不发送消息给模型
      return;
    }
  }

  // 处理模型项目/营地点击，跳转到详情
  if (e.data.eve === 'clickDemo') {
    const payload = {
      ...e.data,
      // 根据当前页面状态补充isGw参数：如果当前在国际模式（非中国）则为true，否则为false
      isGw: String(localStorage.getItem("isChina") !== "1")
    };
    // 存储点击的项目信息
    sessionStorage.setItem("clickProject", JSON.stringify(payload));
    // 根据点击类型跳转到对应详情页面
    const targetRoute = payload.type === 'project'
      ? '/business-display/project-detail'
      : '/business-display/yingdi';
    router.push(targetRoute);
    return;
  }

  // 处理取消操作
  if (e.data.eve === 'cancle') {
    rightList.value = [];
  }

  // 处理iframe加载完成
  if (e.data.eve === "loadOk") {
    console.log('iframe加载完成，重置状态');
    showBack.value = false;
    isShow.value = false;
    showCountryDetail.value = false;
    localStorage.removeItem("isChina");
    sessionStorage.removeItem("clickCountry");
    if (iframe.value) {
      iframe.value.contentWindow.postMessage(
        {
          type: "token",
          data: sessionStorage.getItem('token'),
        },
        "*"
      );
    }
  }
};

/**
 * 处理返回首页的逻辑 - 从详情页面返回时触发
 */
const handleBackToHome = async () => {
  console.log('检测到返回首页标记，完全重置到初始状态');
  sessionStorage.removeItem("shouldResetToInitial");
  
  // 调用统一的重置方法
  resetToInitialState();
  
  // 确保iframe切换到地球模式
  setTimeout(() => {
    if (iframe.value && iframe.value.contentWindow) {
      console.log('向iframe发送地球模式切换消息');
      // 发送切换到地球模式的消息
      iframe.value.contentWindow.postMessage(
        { eve: "changeModel", data: "earth" },
        "*"
      );
      // // 发送取消消息，确保地图状态重置
      // iframe.value.contentWindow.postMessage(
      //   { eve: "cancle" },
      //   "*"
      // );
      // // 发送token
      // iframe.value.contentWindow.postMessage(
      //   {
      //     type: "token",
      //     data: sessionStorage.getItem('token'),
      //   },
      //   "*"
      // );
    }
  }, 500);
  
  // 获取数据后直接返回，不需要恢复之前的状态
  await Promise.all([getForeignInfo(), getInnerInfo()]);
  
  // 确保重置后的项目类型状态生效
  setTimeout(() => {
    console.log('handleBackToHome后重置项目类型为施工状态');
  }, 500);
  
  console.log('首页状态恢复完成');
};

// 监听路由变化，检测从子路由返回时的重置需求
watch(
  () => route.path,
  async (newPath, oldPath) => {
    console.log('路由变化:', oldPath, '->', newPath);
    
    // 当从子路由返回到主路由时，检查返回类型和重置需求
    if (newPath === '/business-display' && 
        oldPath && oldPath.startsWith('/business-display/')) {
      
      console.log('从详情页面返回到主页面，检查返回类型');
      
      const returnType = sessionStorage.getItem("returnType");
      console.log('路由监听器检测到的returnType:', returnType);
      if (returnType === "backHome") {
        console.log('路由监听器：检测到返回首页');
        sessionStorage.removeItem("returnType"); // 清除标记，避免mounted重复处理
        await handleBackToHome();
      } else if (oldPath === '/business-display/video-display' && returnType === "backFromVideo") {
        console.log('路由监听器：检测到从现场视频页面返回，恢复保存状态');
        sessionStorage.removeItem("returnType");
        await handleBackFromVideo();
      } else if (oldPath === '/business-display/video-display' && returnType === "backToCountry") {
        console.log('路由监听器：检测到从现场视频页面返回上一级');
        sessionStorage.removeItem("returnType");
        await handleBackToCountryDetail();
        await Promise.all([getForeignInfo(), getInnerInfo()]);
      } else if (returnType === "backToCountry") {
        console.log('路由监听器：检测到返回国家详情');
        sessionStorage.removeItem("returnType"); // 清除标记，避免mounted重复处理
        await handleBackToCountryDetail();
        await Promise.all([getForeignInfo(), getInnerInfo()]);
      } else if (returnType === "backToChina") {
        console.log('路由监听器：检测到从现场视频页面返回首页');
        sessionStorage.removeItem("returnType");
        await handleBackToHome();
      } else {
        // 检查是否需要重置到初始状态（兜底逻辑）
        const shouldResetToInitial = sessionStorage.getItem("shouldResetToInitial");
        if (shouldResetToInitial === "true") {
          console.log('路由监听器：执行兜底重置逻辑');
          await handleBackToHome();
        }
      }
    }
  },
  { immediate: false }
);

onMounted(async () => {
  // 初始化iframe
  iframe.value = document.getElementById("iframe");
  // 延迟设置 iframe 源，避免首屏阻塞
  const assignIframeSrc = () => {
    iframeSrc.value = 'http://192.168.0.122:8080/zjsj-yydp/indexDp.html';
  };
  if (typeof window !== 'undefined' && 'requestIdleCallback' in window) {
    // @ts-ignore
    requestIdleCallback(assignIframeSrc, { timeout: 1000 });
  } else {
    setTimeout(assignIframeSrc, 0);
  }
  
  // 添加消息监听器
  window.addEventListener("message", handleMessage);

  // 检查返回类型并处理不同的返回场景
  const returnType = sessionStorage.getItem("returnType");
  
  // 添加详细的调试信息
  console.log('=== 业务展示页面mounted开始 ===');
  console.log('检测到的returnType:', returnType);
  console.log('当前sessionStorage状态:', {
    returnType: sessionStorage.getItem("returnType"),
    clickCountry: sessionStorage.getItem("clickCountry"),
    countryLevel: sessionStorage.getItem("countryLevel"),
    countryTitle: sessionStorage.getItem("countryTitle"),
    shouldResetToInitial: sessionStorage.getItem("shouldResetToInitial")
  });
  console.log('当前localStorage状态:', {
    isChina: localStorage.getItem("isChina"),
    clickProject: localStorage.getItem("clickProject")
  });
  
  if (returnType === "backHome") {
    // 从项目详情返回首页：完全重置到初始状态
    console.log('✅ mounted检测到从项目详情返回首页');
    sessionStorage.removeItem("returnType"); // 清除标记
    await handleBackToHome();
    return;
  } else if (returnType === "backToCountry") {
    // 从项目详情返回到国家/地区详情
    console.log('✅ mounted检测到从项目详情返回到国家/地区详情');
    sessionStorage.removeItem("returnType"); // 清除标记
    await handleBackToCountryDetail();
    await Promise.all([getForeignInfo(), getInnerInfo()]);
    return; // 添加return，避免执行后续的正常逻辑
  } else if (returnType === "backFromVideo") {
    // 从视频页面返回，恢复保存状态
    console.log('✅ mounted检测到从现场视频页面返回，恢复保存状态');
    sessionStorage.removeItem("returnType"); // 清除标记
    await handleBackFromVideo();
    return; // 添加return，避免执行后续的正常逻辑
  } else if (returnType === "backToChina") {
    // 从视频页面返回
    console.log('✅ mounted检测到从现场视频页面返回首页');
    sessionStorage.removeItem("returnType"); // 清除标记
    await handleBackToHome();
    return; // 添加return，避免执行后续的正常逻辑
  } else {
    // 正常页面加载逻辑
    // 初始进入页面时也检查一次重置标记（处理直接访问的情况）
    const shouldResetToInitial = sessionStorage.getItem("shouldResetToInitial");
    if (shouldResetToInitial === "true") {
      await handleBackToHome();
      return;
    }

    // 恢复展开状态
    const savedExpandState = sessionStorage.getItem('isExpanded');
    if (savedExpandState === 'true') {
      isExpanded.value = true;
      console.log('恢复展开状态为：展开');
    } else if (savedExpandState === 'false') {
      isExpanded.value = false;
      console.log('恢复展开状态为：收起');
    }
    // 如果没有保存的状态，保持默认的 false

    // 新增：恢复上一次的地图层级状态（仅在非重置情况下）
    const savedCountry = sessionStorage.getItem("clickCountry");
    const savedLevel = sessionStorage.getItem("countryLevel");
    if (savedCountry === "中国") {
      isShow.value = true;
      displayCountryName.value = "中国";
      setTimeout(() => { 
        showBack.value = true; 
        showDoubleButtons.value = true; // 延时2秒显示双按钮
      }, 2000);
      localStorage.setItem("isChina", "1");
    } else if (savedCountry && savedCountry !== "earth") {
      showCountryDetail.value = true;
      currentCountry.value = savedCountry;
      const domestic = localStorage.getItem("isChina") === "1";
      isChina.value = domestic;
      
      // 恢复显示的国家名称 - 点到哪里就显示哪里
      displayCountryName.value = savedCountry;
      
      // 如果是中国境内的省市区，延时2秒显示双按钮
      if (domestic) {
        setTimeout(() => { 
          showDoubleButtons.value = true; 
        }, 2000);
      }
      
      await getInfoByLevel(savedLevel, savedCountry);
      await fetchRightList(savedCountry);
    }

    // 获取数据
    await Promise.all([getForeignInfo(), getInnerInfo()]);
  }
  
  // 向iframe发送token等初始化消息
  if (iframe.value) {
    console.log(iframe.value)

    setTimeout(() => {
        iframe.value.contentWindow.postMessage(
        {
          type: "token",
          data: sessionStorage.getItem('token'),
        },
        "*"
      );
      console.log('发送token')

      // 如果恢复的状态是展开状态，向iframe发送切换到平面模式的消息
      if (isExpanded.value) {
        setTimeout(() => {
          iframe.value.contentWindow.postMessage(
            { eve: "changeModel", data: "plan" },
            "*"
          );
          console.log('恢复展开状态，发送平面模式消息');
        }, 500);
      }
    }, 2000);
  }
});

// 组件卸载时清理监听器
onBeforeUnmount(() => {
  window.removeEventListener("message", handleMessage);
  if (throttleTimer.value) {
    clearTimeout(throttleTimer.value);
  }
  // 重置执行状态
  isBackToMainExecuting.value = false;
  isBackToHomeExecuting.value = false;
});
</script>

<style lang="scss" scoped>
.business-display-container {
  transform-origin: center center;
  position: absolute;
  top: 50%;
  left: 50%;
  overflow: hidden;
  // 具体的width、height、transform由响应式style对象控制
}
.item-title {
  font-size: 30px;
}

.simple-header {
  width: 100%;
  height: 122px;
  position: fixed;
  top: 0px;
  left: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-center {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.title-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.title-bg {
  height: 120px;
}

.title-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -60%);
  width: 100%;
  text-align: center;
}

.platform-title {
  font-size: 40px;
  color: #EFF8FC;
  font-family: YouSheBiaoTiHei, sans-serif;
  font-weight: 400;
  letter-spacing: 3px;
  text-shadow: inset 0px 0px 1px rgba(255, 255, 255, 0.8), 
               0px 0px 7px rgba(130, 165, 255, 0.54), 
               0px 2px 0px rgba(19, 80, 143, 0.66);
  white-space: nowrap;
}

.header-controls {
  position: absolute;
  top: 19px;
  right: 20px;
  display: flex;
  align-items: center;
  gap: 20px;
  z-index: 101;
}

.control-item {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 4px;
  transition: all 0.3s ease;
  pointer-events: auto;
}

.expand-collapse-item {
  flex-direction: column !important;
  align-items: center !important;
  gap: 6px;
  padding: 8px 10px !important;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
  }
  
  .control-icon {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.control-text {
  color: white;
  font-size: 16px;
  font-family: Alibaba PuHuiTi 2.0, sans-serif;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
}

.video-switch {
  flex-direction: column !important;
  align-items: center !important;
  gap: 6px;
  padding: 8px 10px !important;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
  }
  
  .control-icon {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.video-icon {
  width: 32px;
  height: 32px;
}

/* 国家名称显示样式 */
.country-name-display {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-top: 20px;
  z-index: 102;
}

.country-name-text {
  font-family: YouSheBiaoTiHei, sans-serif;
  font-size: 50px;
  color: #EFF8FC;
  font-weight: 400;
  letter-spacing: 8px;
  text-shadow: 
    inset 0px 0px 1px rgba(255, 255, 255, 0.8), 
    0px 0px 7px rgba(130, 165, 255, 0.54), 
    0px 2px 0px rgba(19, 80, 143, 0.66);
  white-space: nowrap;
  text-align: center;
  // background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  backdrop-filter: blur(10px);
  // border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 8px 20px;
}

/* 项目页面的原始样式 */
.project-container {
  pointer-events: none;
  background: transparent;
  z-index: 3;
  transition: all 0.5s ease;
  
  &.expanded {
    transform: scale(1.05);
    filter: brightness(1.1);
  }

  .leftList {
    display: flex;
    flex-direction: column;
    position: relative;
    justify-content: space-around;
    height: 80%;
    top: 40px;
    z-index: 99;
    margin-left: 120px;
    pointer-events: auto;

    .item-img {
      position: relative;
      top: -16px;
    }

    .items {
      display: flex;
      gap: 20px;
      position: relative;

      .item-amount {
        font-family: "xiaoweiLogo";
      }
    }
  }

  .rightList {
    display: flex;
    flex-direction: column;
    position: relative;
    justify-content: space-around;
    height: 80%;
    top: 40px;
    z-index: 99;
    margin-right: 120px;
    padding: 0 20px;
    pointer-events: auto;
    margin-left: 120px;

    .item-img {
      position: relative;
      top: -16px;
    }

    .items {
      display: flex;
      gap: 20px;
      position: relative;

      .item-amount {
        font-family: "xiaoweiLogo";
      }
    }
  }

  .rotate-perspective {
    perspective: 800px;

    .rotate-item1 {
      transform: rotate3d(0, 1, 0, 5.5deg);
    }

    .rotate-item2 {
      transform: rotate3d(0, 1, 0, -5.5deg);
    }
  }
}

.project-type-selector {
  position: absolute;
  /* 放在页面顶部下方约20px（考虑到头部122px高度，取约140px） */
  top: 20px;
  left: 72%;
  transform: translateX(-50%);
  z-index: 100;
  pointer-events: auto;
}

.earth-map-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: all;
  background: url("@/assets/images/xiangmu/wangge.png") no-repeat center center / 100% 100%;
  
  .iframe-container {
    width: 100%;
    height: 100%;
    border: none;
  }
}

.bgfont {
  font-family: TCloudNumber;
  background-image: linear-gradient(to bottom,
      #9AC4FF 0%,
      #FFFFFF 50%,
      #9AC4FF 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
}

.personnel-breakdown {
  margin-top: 10px;
  width: 100%;
}

.breakdown-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 90%;
  margin: 4px auto;
}

.breakdown-label {
  background: linear-gradient(180deg, rgba(0,132,255,0) 0%, #0084FF 50%, rgba(0,132,255,0) 100%);
  border-radius: 0px 0px 0px 0px;
  font-size: 18px;
  padding: 2px 8px;
  color: #fff;
  flex-shrink: 0;
}

.breakdown-value {
  font-weight: bold;
  font-size: 22px;
  color: #fff;
  margin: 0 10px;
  flex-grow: 1;
  text-align: right;
}

.breakdown-unit {
  font-size: 16px;
  color: #fff;
  flex-shrink: 0;
}

/* 国家详情页面样式 */
.header-left {
  position: absolute;
  top: 19px;
  left: 20px;
  z-index: 101;
}

.header-left-double {
  position: absolute;
  top: 19px;
  left: 20px;
  z-index: 101;
  display: flex;
  gap: 15px;
}

.back-button {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 4px;
  transition: all 0.3s ease;
  pointer-events: auto;
  gap: 8px;

  img {
    width: 24px;
    height: 24px;
  }

  span {
    color: white;
    font-size: 18px;
    font-family: Alibaba PuHuiTi 2.0, sans-serif;
    font-weight: normal;
  }

  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
    
    &:hover {
      background: transparent;
    }
  }
}

.title-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -60%);
  width: 100%;
  text-align: center;
}

.platform-title {
  font-size: 32px;
  color: #EFF8FC;
  font-family: YouSheBiaoTiHei, sans-serif;
  font-weight: 400;
  letter-spacing: 2px;
  text-shadow: inset 0px 0px 1px rgba(255, 255, 255, 0.8), 
               0px 0px 7px rgba(130, 165, 255, 0.54), 
               0px 2px 0px rgba(19, 80, 143, 0.66);
  white-space: nowrap;
}

.leftList-china {
  margin-left: 120px !important;
}

.bg-gradient {
  background: linear-gradient(180deg, rgba(0,132,255,0) 0%, #0084FF 50%, rgba(0,132,255,0) 100%);
  border-radius: 0px 0px 0px 0px;
  font-size: 18px;
  margin-right: 6px;
  padding: 2px 8px;
  color: #fff;
}

/* 项目类型交互样式 */
.project-type-clickable {
  cursor: pointer;
  transition: all 0.3s ease;
}

.project-type-selected {
  .item-title {
    color: #00D4FF !important;
    text-shadow: 0 0 8px rgba(0, 212, 255, 0.5);
  }
  
  .item-amount {
    span {
      color: #00D4FF !important;
      text-shadow: 0 0 8px rgba(0, 212, 255, 0.5);
    }
  }
}

.right-list-container {
  position: absolute;
  right: 170px;
  top: 190px;
  width: 700px;
  height: calc(100% - 140px);
  border-radius: 8px;
  color: #fff;
  display: flex;
  flex-direction: column;
  pointer-events: auto;
  z-index: 99;

  .list-header {
    background: url('@/assets/images/map/controlBg.png') no-repeat;
    background-size: 100% 100%;
    text-align: center;
    font-size: 20px;
    height: 40px;
    line-height: 40px;
    font-weight: bold;
    color: #EEF4FF;
    flex-shrink: 0;
  }

  .list-content {
    padding: 10px;
    overflow-y: auto;
    flex-grow: 1;

    &::-webkit-scrollbar {
      width: 4px;
    }
    &::-webkit-scrollbar-thumb {
      background: #0084ff;
      border-radius: 2px;
    }
    &::-webkit-scrollbar-track {
      background: transparent;
    }

    .list-item {
      display: flex;
      align-items: center;
      padding: 10px 5px;
      cursor: pointer;
      border-bottom: 1px solid rgba(0, 132, 255, 0.2);
      transition: background-color 0.3s;

      &:hover {
        background-color: rgba(0, 132, 255, 0.1);
      }

      .item-index {
        margin-right: 30px;
        font-size: 28px;
        width: 38px;
        color: #9ac4ff;
        text-align: center;
      }
      
      .item-name {
        font-size: 30px;
        flex-grow: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}


</style> 