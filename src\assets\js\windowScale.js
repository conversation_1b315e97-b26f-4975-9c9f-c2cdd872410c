import { onMounted, onUnmounted, ref } from "vue";

// * 设计稿尺寸（px）
const baseWidth = 5280;
// 动态获取页面高度，如果小于1000px则使用1620px
const baseHeight = ref(document.documentElement.clientHeight < 1000 ? 1620 : document.documentElement.clientHeight);

/**
 * WindowScale适配Hook - 响应式样式方式
 * @returns {Object} 包含style响应式对象和相关方法
 */
export function useIndex() {
  const style = ref({
    width: `${baseWidth}px`,
    height: `${baseHeight.value}px`,
    transform: "translate(-50%, -50%) scale(1)", // 默认不缩放，垂直水平居中
  });

  // 获取缩放比例
  const getScale = () => {
    // 使用document.documentElement.clientWidth/Height获取可见视口尺寸
    // 这样可以避免非全屏模式下的留白问题
    const w = document.documentElement.clientWidth / baseWidth;
    const h = document.documentElement.clientHeight / baseHeight.value;
    return w < h ? w : h;
  };

  // 设置缩放比例
  const setScale = () => {
    // 更新动态高度，如果小于1000px则使用1620px
    const currentHeight = document.documentElement.clientHeight;
    baseHeight.value = currentHeight < 1000 ? 1620 : currentHeight;
    // 更新样式中的高度
    style.value.height = `${baseHeight.value}px`;

    const scale = getScale();
    style.value.transform = `translate(-50%, -50%) scale(${scale})`;
    console.log(`WindowScale: 设置缩放比例 ${scale}, 元素尺寸 ${baseWidth}x${baseHeight.value}, 视口尺寸 ${document.documentElement.clientWidth}x${document.documentElement.clientHeight}`);
  };

  // 防抖函数
  const debounce = (fn, t) => {
    const delay = t || 500;
    let timer;
    // eslint-disable-next-line func-names
    return function () {
      // eslint-disable-next-line prefer-rest-params
      const args = arguments;
      if (timer) {
        clearTimeout(timer);
      }
      const context = this;
      timer = setTimeout(() => {
        timer = null;
        fn.apply(context, args);
      }, delay);
    };
  };

  const resize = debounce(() => setScale(), 100);

  // 改变窗口大小重新绘制
  const windowDraw = () => {
    window.addEventListener("resize", resize);
  };

  // Vue生命周期管理
  onMounted(() => {
    // 确保在DOM挂载后获取正确的高度，如果小于1000px则使用1620px
    const currentHeight = document.documentElement.clientHeight;
    baseHeight.value = currentHeight < 1000 ? 1620 : currentHeight;
    style.value.height = `${baseHeight.value}px`;
    setScale(); // 初始化时设置缩放
    windowDraw();
  });

  onUnmounted(() => {
    window.removeEventListener("resize", resize);
  });

  return { style, setScale, getScale };
}
