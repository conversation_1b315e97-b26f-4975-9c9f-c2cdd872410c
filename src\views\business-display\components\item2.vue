<template>
  <div class="left-content">
    <!-- 上半部分：安全隐患 -->
    <div class="left-item one">
      <!-- 第一行：安全隐患标题 -->
      <!-- <div class="project-title">
        {{ title }}
      </div> -->

      <!-- 第二行：标题栏 -->
      <div class="info-section">
        <div class="info-section-bg">{{ t('projectDetail.qualitySafety.safetyHazards') }}</div>
      </div>

      <!-- 第三行：排查记录和隐患记录 -->
      <div class="status-section">
        <div class="status-item">
          <svg-icon name="troubleshooting" color="#fff" style="width: 33.6px;height: 33.6px;margin-top: 4.8px;" />
          <div class="status-content">
            <div class="status-label">{{ t('projectDetail.qualitySafety.inspectionRecords') }}: <span
                class="recordNum">{{
                  safetyData.排查记录 || 0 }}</span>
              <span class="unit">条</span>
            </div>
          </div>
        </div>
        <div class="status-item">
          <svg-icon name="hiddenDanger" color="#fff" style="width: 33.6px;height: 33.6px;margin-top: 4.8px;" />
          <div class="status-content">
            <div class="status-label">{{ t('projectDetail.qualitySafety.hazardRecords') }}: <span
                class="recordNumRed">{{ safetyData.严重隐患 || 0 }}</span>
              <span class="unit">条</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 第四行：已销项/待复查/待整改/超期 -->
      <div class="issue-section">
        <div class="issue-row">
          <div class="issue-item">
            <div class="issue-label">{{ t('projectDetail.qualitySafety.status.resolved') }}:</div>
            <div class="issue-value"> <span class="blueFont">{{ safetyData.已销项 || 0 }}</span> 条</div>
          </div>
          <div class="issue-item">
            <div class="issue-label">{{ t('projectDetail.qualitySafety.status.pendingReview') }}:</div>
            <div class="issue-value"> <span class="yellowFont">{{ safetyData.待复查 || 0 }}</span> 条</div>
          </div>
        </div>
        <div class="divider-line"></div>
        <div class="issue-row">
          <div class="issue-item">
            <div class="issue-label">{{ t('projectDetail.qualitySafety.status.pendingRectification') }}</div>
            <div class="issue-value"> <span class="yellowFont">{{ safetyData.待整改 || 0 }}</span>条</div>
          </div>
          <div class="issue-item">
            <div class="issue-label">{{ t('projectDetail.qualitySafety.status.overdue') }}</div>
            <div class="issue-value"> <span class="redFont">{{ safetyData.超期 || 0 }}</span>条</div>
          </div>
        </div>
      </div>

      <!-- 第五行：隐患数量图表 -->
      <div class="chart-section">
        <div class="chart">
          <BarChart :chartData="safetyChartData" color="255,213,0" topColor="223,192,66"></BarChart>
        </div>
      </div>

      <!-- 第二行：标题栏 -->
      <div class="info-section">
        <div class="info-section-bg">{{ t('projectDetail.qualitySafety.qualityIssues') }}</div>
      </div>

      <!-- 第三行：质量问题数据展示 - 三个指标一行 -->
      <div class="status-section three-items">
        <div class="status-item">
          <svg-icon name="hiddenDanger" color="#fff" style="width: 28px;height: 28px;margin-top: 4px;" />
          <div class="status-content">
            <div class="status-label">{{ t('projectDetail.qualitySafety.hazardTotal') }}: <span class="recordNum"
                style="font-size: 22px;">{{ qualityData.隐患总数 || 0
                }}</span>
              <span class="unit">{{ t('projectDetail.qualitySafety.units.count') }}</span>
            </div>
          </div>
        </div>
        <div class="status-item">
          <svg-icon name="correction" color="#fff" style="width: 28px;height: 28px;margin-top: 4px;" />
          <div class="status-content">
            <div class="status-label">{{ t('projectDetail.qualitySafety.rectificationRate') }}: <span class="recordNum"
                style="font-size: 22px;">{{ qualityData.整改率 || 0
                }}</span>
              <span class="unit">{{ t('projectDetail.qualitySafety.units.percent') }}</span>
            </div>
          </div>
        </div>
        <div class="status-item">
          <svg-icon name="timelyRectification" color="#fff" style="width: 28px;height: 28px;margin-top: 4px;" />
          <div class="status-content">
            <div class="status-label">{{ t('projectDetail.qualitySafety.timelyRectificationRate') }}: <span
                class="recordNum" style="font-size: 22px;">{{ qualityData.整改及时率 || 0
                }}</span>
              <span class="unit">{{ t('projectDetail.qualitySafety.units.percent') }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 第四行：合格/待核验/待复查/待整改 -->
      <div class="issue-section">
        <div class="issue-bg"></div>
        <div class="issue-row">
          <div class="issue-item">
            <div class="issue-label">{{ t('projectDetail.qualitySafety.status.qualified') }}</div>
            <div class="issue-value "> <span class="blueFont">{{ qualityData.合格 || 0 }}</span> 条</div>
          </div>
          <div class="issue-item">
            <div class="issue-label">{{ t('projectDetail.qualitySafety.status.pendingVerification') }}</div>
            <div class="issue-value "> <span class="yellowFont">{{ qualityData.待核验 || 0 }}</span> 条</div>
          </div>
        </div>
        <div class="divider-line"></div>
        <div class="issue-row">
          <div class="issue-item">
            <div class="issue-label">{{ t('projectDetail.qualitySafety.status.pendingReview') }}</div>
            <div class="issue-value "> <span class="yellowFont">{{ qualityData.待复查 || 0 }}</span> 条</div>
          </div>
          <div class="issue-item">
            <div class="issue-label">{{ t('projectDetail.qualitySafety.status.pendingRectification') }}</div>
            <div class="issue-value "> <span class="redFont">{{ qualityData.待整改 || 0 }}</span>条</div>
          </div>
        </div>
      </div>

      <!-- 第五行：质量问题统计图表 (组合图表) -->
      <div class="chart-section">
        <div class="chart">
          <CombinedChart 
            :barData="qualityBarData" 
            :lineData="qualityLineData" 
            :xData="qualityMonths" 
            color="255,213,0" 
            topColor="223,192,66">
          </CombinedChart>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup>
import { nextTick, ref, watch, markRaw, onMounted, computed } from "vue";
import { getImg, formatNumber } from "@/utils/method";
import * as echarts from "echarts";
import PieChart from "./PieChart.vue";
import BarChart from "./BarChart.vue";
import WaterBall from "./WaterBall.vue";
import LineChart from "./LineChart.vue";
import RotatingCircle from "@/components/RotatingCircle.vue";
import TitleBar from "@/components/TitleBar.vue";
import CombinedChart from "./CombinedChart.vue";
import axios from "axios";
import request from "@/utils/request";
import { useI18n } from 'vue-i18n';
import SvgIcon from '@/components/SvgIcon.vue';

const { t } = useI18n();

const props = defineProps({
  projectData: {
    type: Object,
    default: () => ({}),
  },
});

const title = ref(t('projectDetail.overview.projectTitle'));
const safetyData = ref({
  排查记录: 0,
  严重隐患: 0,
  已销项: 0,
  待复查: 0,
  待整改: 0,
  超期: 0,
  "2月": "0:0:0.00",
  "3月": "0:0:0.00",
  "4月": "0:0:0.00",
  "5月": "0:0:0.00",
  "6月": "0:0:0.00",
  "7月": "0:0:0.00"
});

// 新增质量数据
const qualityData = ref({
  整改率: "0",
  整改及时率: "0",
  合格: 0,
  待复查: 0,
  待核验: 0,
  待整改: 0,
  隐患总数: 0,
  严重隐患: 0,
  "2月": "0:0:0.00",
  "3月": "0:0:0.00",
  "4月": "0:0:0.00",
  "5月": "0:0:0.00",
  "6月": "0:0:0.00",
  "7月": "0:0:0.00"
});

// 计算用于安全隐患图表的数据
const safetyChartData = computed(() => {
  // 获取接口返回的月份键（3月、4月等）
  const months = Object.keys(safetyData.value).filter(key => key.includes('月'));
  
  // 正确的月份排序逻辑
  months.sort((a, b) => {
    const monthA = parseInt(a.replace('月', ''));
    const monthB = parseInt(b.replace('月', ''));
    return monthA - monthB;
  });
  
  const chartData = {
    name: "隐患个数",
    secondName: "严重隐患个数",
    thirdName: "占比",
    data: [],
    percentages: [],
    months: months,
    rawData: {} // 存储原始数据
  };

  months.forEach(month => {
    if (safetyData.value[month]) {
      // 从"隐患个数:严重隐患个数:占比"格式中提取数据
      const parts = safetyData.value[month].split(':');
      // 使用严重隐患个数作为图表数据
      const value = parseInt(parts[0]) || 0;
      const percentage = parts.length > 2 ? parseFloat(parts[2]) : 0;

      // 存储原始数据
      chartData.rawData[month] = safetyData.value[month];
      
      chartData.data.push(value);
      chartData.percentages.push(percentage.toFixed(2));
    } else {
      chartData.data.push(0);
      chartData.percentages.push("0.00");
    }
  });

  return chartData;
});


// 格式化项目数据
const formatProjectData = (data) => {
  if (data) {
    title.value = data.项目名称;
    // 可以在这里处理其他数据
  }
};

// 获取安全隐患数据
const fetchSafetyData = async () => {
  try {
    // 从sessionStorage获取项目信息
    const projectInfo = JSON.parse(sessionStorage.getItem("clickProject") || "{}");
    const projectId = projectInfo.code || "";

    if (!projectId) {
      console.error("无法获取项目ID");
      return;
    }
    // 直接请求安全隐患数据接口
    const response = await request.get('/globalManage/zjmanage/largescreen/getSafety', {
      params: { id: projectId },
    });

    if (response.code === 0 && response.data) {
      safetyData.value = response.data;

      // 确保超期字段为数字
      if (typeof safetyData.value.超期 === 'string') {
        safetyData.value.超期 = parseInt(safetyData.value.超期) || 0;
      }
    } else {
      console.error("获取安全隐患数据失败:", response.msg);
    }
  } catch (error) {
    console.error("请求安全隐患数据出错:", error);
  }
};

// 新增获取质量数据函数
const fetchQualityData = async () => {
  try {
    // 从sessionStorage获取项目信息
    const projectInfo = JSON.parse(sessionStorage.getItem("clickProject") || "{}");
    const projectId = projectInfo.code || "";

    if (!projectId) {
      console.error("无法获取项目ID");
      return;
    }
    // 请求质量数据接口
    const response = await request.get('/globalManage/zjmanage/largescreen/getQuality', {
      params: { id: projectId },
    });

    if (response.code === 0 && response.data) {
      qualityData.value = response.data;

      // 确保数值字段为数字类型
      ['合格', '待复查', '待核验', '待整改'].forEach(field => {
        if (typeof qualityData.value[field] === 'string') {
          qualityData.value[field] = parseInt(qualityData.value[field]) || 0;
        }
      });

      // 确保百分比字段为字符串类型
      ['整改率', '整改及时率'].forEach(field => {
        if (typeof qualityData.value[field] === 'number') {
          qualityData.value[field] = qualityData.value[field].toString();
        }
      });
    } else {
      console.error("获取质量数据失败:", response.msg);
    }
  } catch (error) {
    console.error("请求质量数据出错:", error);
  }
};

onMounted(() => {
  formatProjectData(props.projectData);
  fetchSafetyData();
  fetchQualityData(); // 新增质量数据获取
});

watch(
  () => props.projectData,
  (newData) => {
    formatProjectData(newData);
  }
);

const list1 = ref([
  {
    id: 1,
    title: "排查记录",
    num: 76,
    icon: getImg("project/icon7.png"),
  },
  {
    id: 2,
    title: "严重隐患",
    num: 6666,
    icon: getImg("project/icon8.png"),
  },
]);

const pieData1 = ref([
  {
    value: 1914,
    name: "已销项",
    itemStyle: { color: "rgba(0,255,170,1)" },
  },
  {
    value: 1601,
    name: "待复查",
    itemStyle: { color: "rgba(0,255,255, 1)" },
  },
  {
    value: 1537,
    name: "待整改",
    itemStyle: { color: "rgba(247,147,30, 1)" },
  },
  {
    value: 1007,
    name: "超期",
    itemStyle: { color: "rgba(237,28,36, 1)" },
  },
]);

const pieData2 = ref([
  {
    value: 56,
    name: "合格",
    itemStyle: { color: "rgba(0,255,170,1)" },
  },
  {
    value: 79,
    name: "待核验",
    itemStyle: { color: "rgba(0,255,255, 1)" },
  },
  {
    value: 49,
    name: "待复查",
    itemStyle: { color: "rgba(247,147,30, 1)" },
  },
  {
    value: 27,
    name: "待整改",
    itemStyle: { color: "rgba(237,28,36, 1)" },
  },
]);

const legend = ref(["折旧金额"]);
const xData = ref(["1月", "2月", "3月", "4月", "5月"]);
const chartDate = ref([
  {
    name: "折旧金额",
    itemStyle: { color: "rgba(5,85,163, 1)" },
    data: [60, 85, 56, 33, 20],
  },
]);

// 修复质量数据的月份处理
const qualityMonths = computed(() => {
  // 获取接口返回的月份键（3月、4月等）
  const months = Object.keys(qualityData.value).filter(key => key.includes('月'));
  
  // 正确的月份排序逻辑
  months.sort((a, b) => {
    const monthA = parseInt(a.replace('月', ''));
    const monthB = parseInt(b.replace('月', ''));
    return monthA - monthB;
  });
  
  return months;
});

const qualityBarData = computed(() => {
  const months = qualityMonths.value;
  const chartData = {
    name: "问题个数",
    secondName: "严重问题个数",
    thirdName: "占比",
    data: [],
    months: months,
    rawData: {} // 存储原始数据
  };

  months.forEach(month => {
    if (qualityData.value[month]) {
      // 从"问题个数:严重问题个数:占比"格式中提取数据
      const parts = qualityData.value[month].split(':');
      // 使用问题个数作为图表数据
      const value = parseInt(parts[0]) || 0;
      
      // 存储原始数据
      chartData.rawData[month] = qualityData.value[month];
      
      chartData.data.push(value);
    } else {
      chartData.data.push(0);
    }
  });

  return chartData;
});

const qualityLineData = computed(() => {
  const months = qualityMonths.value;
  const chartData = {
    name: "占比",
    itemStyle: { 
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [{
          offset: 0, color: '#E54545'
        }, {
          offset: 0.5, color: '#FF7A7A'
        }, {
          offset: 1, color: '#E54545'
        }]
      }
    },
    data: []
  };

  months.forEach(month => {
    if (qualityData.value[month]) {
      const parts = qualityData.value[month].split(':');
      const percentage = parts.length > 2 ? parseFloat(parts[2]) : 0;
      chartData.data.push(percentage);
    } else {
      chartData.data.push(0);
    }
  });

  return chartData;
});

</script>

<style lang="scss" scoped>
.left-content {
  width: 100%;
  height: 100%;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 15.6px;
  position: relative;
  background: rgba(10, 15, 26, 0.8);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(5px);
  z-index: 1;
  overflow-y: auto;
  
  // 隐藏滚动条
  scrollbar-width: none;
  -ms-overflow-style: none;
  &::-webkit-scrollbar {
    display: none;
  }

  .left-item {
    width: 100%;
    display: flex;
    flex-direction: column;
    z-index: 2;
    margin-bottom: 12px;
  }
}

/* 项目标题样式 */
.project-title {
  width: 100%;
  max-width: 624px;
  height: 93.6px;
  background: url("@/assets/images/map/title.png") no-repeat;
  background-size: 100% 100%;
  font-family: PangMenZhengDao, PangMenZhengDao;
  font-weight: 400;
  font-size: 31.2px;
  color: #F2F3FF;
  line-height: 37.44px;
  letter-spacing: 1.56px;
  text-shadow: 0px 0px 13px #0094FF;
  text-align: left;
  font-style: normal;
  text-transform: none;
  display: flex;
  align-items: center;
  padding-left: 46.8px;
}

/* 信息部分标题样式 */
.info-section {
  width: 100%;
  max-width: 624px;
  height: 54.6px;
  margin: 39px 0 46.8px 0;

  .info-section-bg {
    width: 100%;
    height: 62.4px;
    background: url("@/assets/images/map/projectInfo.png") no-repeat;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    padding-left: 62.4px;
    font-family: PangMenZhengDao, PangMenZhengDao;
    font-size: 31.2px;
    color: #FFFFFF;
    line-height: 35.88px;
    letter-spacing: 3.12px;
    text-align: justified;
    font-style: normal;
    text-transform: none;
  }
}

/* 状态信息样式 */
.status-section {
  width: 100%;
  height: 65.52px;
  display: flex;
  margin-bottom: 31.2px;
  background: rgba(12,32,77,0.24);
  box-shadow: inset 0px 0px 6px 0px #4AA8FC, inset 0px 0px 3px 0px #4AA8FC;
  border-radius: 4px 4px 4px 4px;
  border: 1px solid;
  border-image: linear-gradient(180deg, rgba(38, 85, 150, 1), rgba(12, 27, 48, 1)) 1 1; 

  &.three-items {
    .status-item {
      width: 33.33%;
      
      .status-content {
        .status-label {
          font-size: 22px;
          line-height: 24px;
        }
      }
    }
  }

  .status-item {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50%;

    .status-icon {
      width: 96px;
      height: 96px;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .status-content {
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding-left: 15.6px;

      .status-label {
        font-family: PingFang SC, PingFang SC;
        font-weight: 500;
        font-size: 21.84px;
        color: #DBE9FF;
        line-height: 29.64px;
        text-align: center;
        font-style: normal;
        text-transform: none;
        white-space: nowrap;
      }

      .status-value {
        display: flex;
        align-items: baseline;
        font-family: TCloudNumber;
        font-size: 43.68px;
        font-weight: bold;
        background-image: linear-gradient(to bottom,
            #9AC4FF 0%,
            #FFFFFF 62%,
            #9AC4FF 100%);
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        color: transparent;

        .unit {
          font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
          font-weight: normal;
          font-size: 25px;
          color: rgba(219, 233, 255, 0.8);
          line-height: 31.2px;
          text-align: right;
          font-style: normal;
          text-transform: none;
        }
      }
    }
  }
}

/* 问题状态区域 */
.issue-section {
  width: 100%;
  height: 187.2px;
  position: relative;
  // background: transparent !important;  
  background: url("@/assets/images/map/fourBg.png") no-repeat;
      background-size: 100% 100%;
      background-repeat: no-repeat;
  .issue-row {
    display: flex;
    height: 50%;
    padding: 0 31.2px;
    height: 78px;

    .issue-item {
      width: 50%;
      display: flex;
      justify-content: space-around;
      align-items: center;
      padding-right: 31.2px;

      .issue-label {
        font-size: 21.84px;
        color: #ffffff;
      }
    }
  }

  // .divider-line {
  //   width: 90%;
  //   height: 1px;
  //   background: rgba(255, 255, 255, 0.2);
  //   margin: 0 auto;
  // }
}

/* 图表区域 */
.chart-section {
  width: 100%;
  height: 280.8px;

  .chart-title {
    width: 100%;
    height: 31.2px;
    background: url('@/assets/images/project/sTitle.png') no-repeat;
    background-size: 100% 100%;
    box-sizing: border-box;
    padding-left: 31.2px;
    font-size: 21.84px;
    line-height: 26.52px;
    margin-top: 15.6px;
    margin-bottom: 15.6px;
    font-weight: 300;
    color: #ffffff;
  }

  .chart {
    width: 100%;
    height: 100%;
  }
}

.recordNum {
  font-family: 'TCloudNumber';
  font-size: 34.32px;
  font-weight: bold;
  background-image: linear-gradient(to bottom,
      #9AC4FF 0%,
      #FFFFFF 62%,
      #9AC4FF 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
}

.recordNumRed {
  font-family: 'TCloudNumber';
  font-size: 34.32px;
  background-image: linear-gradient(to bottom,
      #FE6263 0%,
      #FFFFFF 50%,
      #FE6263 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
  font-weight: bold;
}

.blueFont {
  font-family: TCloudNumber;
  font-weight: 400;
  font-size: 31.2px;
  color: #79BFFF;
  line-height: 43.68px;
  text-align: right;
  font-style: normal;
  text-transform: none;
}

.redFont {
  font-family: TCloudNumber;
  font-weight: 400;
  font-size: 31.2px;
  color: #FF6262;
  line-height: 43.68px;
  text-align: right;
  font-style: normal;
  text-transform: none;
}

.yellowFont {
  font-family: TCloudNumber;
  font-weight: 400;
  font-size: 31.2px;
  color: #FCD494;
  line-height: 43.68px;
  text-align: right;
  font-style: normal;
  text-transform: none;
}
</style>
