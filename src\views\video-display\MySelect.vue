<script setup>
import { getCurrentInstance, ref, watchEffect } from "vue";
import { onClickOutside } from "@vueuse/core";
const props = defineProps({
  options: { type: Array, required: true },
  selectValue: { type: null },
  iconUrl:{type: String },
  style:{ type: String}
});

const { proxy } = getCurrentInstance();

const show = ref(false);
// const selectValue = ref(props.selectValue !== undefined ?  props.selectValue : props.options[0].communityName);
const target = ref(null);
onClickOutside(target, () => {
  show.value = false;
});
// watchEffect(() => {
//   selectValue.value = props.selectValue !== undefined ? props.selectValue : props.options[0].communityName;
// });
const getOption = (option) => {
  proxy.$emit("getOption", option);
  // selectValue.value = option;
  show.value = false;
};

const resetSelect = () => {
  proxy.$emit("resetSelect");
  show.value = false;
};

</script>
 
<template>
    <div class="custom-select" ref="target">
        <div class="selected-option" @click="show = !show">
            <!-- <img :src="iconUrl" class="w-[30px] h-[30px] ml-[30px]" alt=""> -->
            <span class="text-[30px] truncate ml-[30px]" :style="style">{{ selectValue && selectValue.title ? $t(`messages.${selectValue.title}`) : selectValue.name }} 
              <span>{{ selectValue.type && selectValue.type.includes('tdly') ? $t(`messages.years`): '' }}</span>
            </span>
            <!-- <img v-if="!show" class="icon" src="@/assets/images/home/<USER>" alt="">
            <img v-else class="icon" src="@/assets/images/home/<USER>" alt=""> -->
            <!-- 调试信息 -->
            <!-- {{ JSON.stringify(selectValue) }} -->
            <div class="reset-icon" @click.stop="resetSelect" v-if="selectValue && selectValue.id && selectValue.id !== '' && selectValue.name !== '请选择国家'">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
        </div>
        <transition name="dropdown" v-show="show">
          <ul class="options-list">
            <li
              v-for="item in options"
              :key="item.id"
              :class="['option', selectValue.name == item.name && 'active']"
              @click="getOption(item)"
            >
                <!-- {{ item.name }} -->
                {{ item.title ? $t(`messages.${item.title}`) : item.name }} 
                <span class="ml-[5px]">{{selectValue.type && selectValue.type.includes('tdly') ? $t(`messages.years`): '' }}</span>
            </li>
          </ul>
        </transition>
    </div>
</template>
 
<style lang="scss" scoped>
.custom-select {
  width: 280px !important;
  height: 75px;
  font-size: 30px;
  background: #395373;
  box-shadow: 0px -1px 2px 0px rgba(0,0,0,0.5);
  position: relative;
  .selected-option {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 75px;
    line-height: 60px;
    cursor: pointer;
    .icon {
      width: 8px;
      height: 6px;
      margin-right: 15px;
      // margin-left: 80px;
      // position: absolute;
      // top: 50%;
      // right: 15px;
      // transform: translateY(-50%);
    }
    .reset-icon {
      position: absolute;
      right: 15px;
      top: 50%;
      transform: translateY(-50%);
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      color: #fff;
      opacity: 0.7;
      transition: all 0.3s ease;
      border-radius: 50%;
      
      &:hover {
        opacity: 1;
        background: rgba(255, 255, 255, 0.1);
        color: #ff6b6b;
      }
      
      svg {
        width: 20px;
        height: 20px;
      }
    }
    >span {
      flex-grow: 1;
      margin-right: 50px; // 为删除图标留出空间
      // width: 220px;
    }
  }
  .options-list {
    width: 390px !important;
    max-height: 375px;
    overflow-y: auto;
    position: absolute;
    top: 75px;
    left: 0;
    background: #2D4159;
    box-shadow: 0px 2px 10px 3px rgba(0,0,0,0.2), 0px 5px 10px 0px rgba(255,255,255,0.05);
    border-radius: 6px;
    border-top: none;
    padding: 10px;
    margin: 0;
    box-sizing: border-box;
    z-index: 200;
    color: white;
    transition: all 0.3s ease-in-out;
  }
  .option {
    width: 100%;
    // height: 40px;
    display: flex;
    align-items: center;
    justify-content: left;
    cursor: pointer;
    font-size: 30px;
    color: white;
    box-sizing: border-box;
    padding: 12px 12px 12px 22px;
    // padding-left: 10px;
    text-align:left;
    margin-bottom: 12px;
    white-space: nowrap;
  }
  .option:hover {
    background: linear-gradient(0deg, #2D4159, #334A66);
    box-shadow: 0px 5px 9px 1px rgba(0,0,0,0.3) inset, 0px -2px 4px 2px rgba(255,255,255,0.08) inset;
    border-radius: 4px;
    color: #33BBFF;
    font-weight: bold;
  }
 
  .active {
    background: linear-gradient(0deg, #2D4159, #334A66);
    box-shadow: 0px 5px 9px 1px rgba(0,0,0,0.3) inset, 0px -2px 4px 2px rgba(255,255,255,0.08) inset;
    border-radius: 4px;
    color: #33BBFF;
    font-weight: bold;
  }
}
/* 自定义滚动条样式 */
.options-list::-webkit-scrollbar {
  width: 6px; /* 滚动条宽度 */
}
.options-list::-webkit-scrollbar-thumb {
  background-color: #33BBFF; /* 滚动条滑块颜色 */
  border-radius: 3px; /* 滚动条滑块圆角 */
  box-shadow: 1px 0px 2px 0px rgba(255,255,255,0.5);
}
.options-list::-webkit-scrollbar-track {
  background: #27384D;
  box-shadow: 1px 0px 2px 0px rgba(0,0,0,0.3);
  border-radius: 3px;
  box-sizing: border-box;
  padding: 10px 0;
}
@media (min-aspect-ratio: 16/9) {
  .custom-select {
    width: 35rem;
    .selected-option {
      >span {
        margin-right: 65px; // 为删除图标留出更多空间
        // width: 22rem;
      }
      .reset-icon {
        right: 20px;
        width: 35px;
        height: 35px;
        
        svg {
          width: 24px;
          height: 24px;
        }
      }
    }
    .options-list {
      width: 35rem;
    }
  }
}
</style>