/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    DeviceSelector: typeof import('./src/components/DeviceSelector.vue')['default']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    RotatingCircle: typeof import('./src/components/RotatingCircle.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SvgIcon: typeof import('./src/components/SvgIcon.vue')['default']
    TitleBar: typeof import('./src/components/TitleBar.vue')['default']
    TpCellPlayer: typeof import('./src/components/TpCellPlayer.vue')['default']
  }
}
