{"name": "lian<PERSON>", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:zip": "cross-env VITE_USE_ZIP=true vite build", "preview": "vite preview", "deploy:dev": "deploy-cli-service deploy --mode dev"}, "dependencies": {"@rollup/plugin-alias": "^5.1.0", "@vueuse/core": "^4.9.0", "axios": "^1.7.2", "echarts": "^5.5.1", "echarts-gl": "^2.0.9", "echarts-liquidfill": "^3.1.0", "element-plus": "^2.7.2", "pinia": "^2.1.7", "sass": "^1.32.13", "scss": "^0.2.4", "three": "^0.174.0", "v-scale-screen": "^2.2.0", "vue": "^3.3.4", "vue-clipboard3": "^2.0.0", "vue-i18n": "^9.13.1", "vue-responsive-video-background-player": "^2.4.1", "vue-router": "^4.4.0", "vue3-count-to": "^1.1.2"}, "devDependencies": {"@vitejs/plugin-vue": "^4.2.3", "autoprefixer": "^10.4.19", "cross-env": "^7.0.3", "deploy-cli-service": "^1.5.3", "postcss": "^8.4.38", "tailwindcss": "^3.4.4", "terser": "^5.39.0", "unplugin-auto-import": "^0.16.6", "unplugin-vue-components": "^0.25.2", "vite": "^4.4.5", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-zip-file": "^4.1.0"}}