<template>
  <div class="player">
    <!-- <div class="title">
      {{ props.cell.name || "暂无视频" }}
    </div> -->

    <video v-if="props.cell.url" ref="videoRef" controls autoplay muted></video>
    <div
      v-else
      class="w-full h-full text-[20px] flex items-center justify-center bg-gray-900 text-[#a3d2eb]"
    >
      {{ $t("security.noVideo") }}
    </div>
  </div>
</template>

<script setup>
import { ref, watch, nextTick, onBeforeUnmount } from "vue";
import videoHls from "@/utils/Hls";

let hls = null;

const props = defineProps({
  cell: {
    type: Object,
    required: true,
  },
});

// 只监听URL的变化，而不是整个cell对象
watch(
  () => props.cell.url,
  (newUrl) => {
    if (newUrl) {
      nextTick(() => {
        if (hls) destroy();
        initLoad();
      });
    }
  },
  { immediate: true }
);

const videoRef = ref();

function initLoad() {
  hls = new videoHls(videoRef.value, props.cell.url);
}

function play() {
  hls.play();
}

function pause() {
  hls.pause();
}

function destroy() {
  hls.destroy();
}

onBeforeUnmount(() => {
  if (hls) destroy();
});

defineExpose({ play, pause, destroy });
</script>

<style lang="scss" scoped>
.player {
  position: relative;
  background-color: #fff;
  height: 100%;
  // border: 1px solid white;
  color: white;
  text-align: center;

  .title {
    position: absolute;
    z-index: 10;
    top: 0;
    width: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    font-size: 12px;
  }
  video {
    object-fit: fill;
    width: 100%;
    height: 100%;
  }
  .highlight {
    background-color: #1ea2f6;
  }
  video {
    object-fit: fill;
    width: 100%;
    height: 100%;

    /* 放大所有控制按钮 */
    &::-webkit-media-controls-play-button,
    &::-webkit-media-controls-mute-button,
    &::-webkit-media-controls-fullscreen-button,
    &::-webkit-media-controls-toggle-closed-captions-button {
      transform: scale(1.5);
    }
    &::-webkit-media-controls-timeline {
      transform: none;
    }
  }
}
</style>
