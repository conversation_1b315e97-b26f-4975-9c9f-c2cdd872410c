<script>
  import { watch } from "vue";
  import { useRoute } from "vue-router";
  
  export default {
    name: "appRef",
    setup() {
      const route = useRoute();
      
      // 由于只保留使用 windowScale.js 的页面，不再需要复杂的缩放逻辑
      console.log('App.vue: 项目统一使用 windowScale.js 进行适配');
      
      return {};
    },
    mounted() {
      // 确保 App 容器不影响子页面的缩放
      const appElement = document.getElementById("app");
      if (appElement) {
        appElement.style.transform = "";
        appElement.style.width = "";
        appElement.style.height = "";
        appElement.style.left = "";
      }
    },
  };
</script>

<template>
  <router-view v-slot="{ Component }">
    <keep-alive>
      <component v-if="$route.meta.keepAlive" :is="Component" />
    </keep-alive>
    <component v-if="!$route.meta.keepAlive" :is="Component" />
  </router-view>
</template>

<style scoped></style>
