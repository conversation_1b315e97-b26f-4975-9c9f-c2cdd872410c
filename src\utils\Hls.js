class videoHls {
  hls = null;
  video = null;
  url = null;
  constructor(videoDom, url) {
    this.video = videoDom;
    this.url = url;
    url && this.init();
  }
  init() {
    if (Hls.isSupported()) {
      console.log("hello hls.js!");
    }
    this.hls = new Hls(); // bind them together
    this.hls.loadSource(this.url);
    this.hls.attachMedia(this.video); // MEDIA_ATTACHED event is fired by hls object once MediaSource is ready
    let _this = this;
    this.hls.on(Hls.Events.MEDIA_ATTACHED, function () {
      console.log("video and hls.js are now bound together !");
      hls.on(Hls.Events.MANIFEST_PARSED, function (event, data) {
        _this.play();
      });
    });

    this.hls.on(Hls.Events.ERROR, function (event, data) {
      if (data.fatal) {
        switch (data.type) {
          case Hls.ErrorTypes.NETWORK_ERROR: // try to recover network error
            console.log("fatal network error encountered, try to recover");
            this.hls.startLoad();
            break;
          case Hls.ErrorTypes.MEDIA_ERROR:
            console.log("fatal media error encountered, try to recover");
            this.hls.recoverMediaError();
            break;
          default: // cannot recover
            this.hls.destroy();
            break;
        }
      }
    });
  }

  play() {
    this.video.play();
  }

  pause() {
    this.video.pause();
  }

  destroy() {
    this.pause();
    this.hls.destroy();
    this.hls = null;
    // this.video.destroy();
    this.video.src = '';
    this.video.load();
  }
}

export default videoHls;
