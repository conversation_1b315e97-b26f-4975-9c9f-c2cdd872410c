<template>
  <div class="video-display-container">
    <!-- 简化的头部 - 保持原有标题，添加返回按钮 -->
    <div class="simple-header" :style="{ display: isFullscreenVisible ? 'none' : 'flex' }">
      <!-- 中间标题 -->
      <div class="header-center">
        <div class="title-wrapper">
          <img src="@/assets/images/header/xcsp.png" alt="背景" class="title-bg" />
        </div>
      </div>

      <!-- 返回首页按钮 -->
      <div class="backHome-button" @click="backHome">
        <img src="@/assets/images/header/back.png" alt="" />
        <span>返回首页</span>
      </div>

      <!-- 返回上一级按钮 -->
      <div class="back-button" @click="back">
        <span>返回上一级</span>
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content-area">
      <!-- 视频内容卡片区域 -->
      <div class="video-cards-container">
        <!-- 重点工程卡片 -->
        <div class="carousel-card" :class="{
          'active': activeCardIndex === 0,
          'right': activeCardIndex === 3,
          'left': activeCardIndex === 1 || activeCardIndex === 2,
          'left-far': activeCardIndex === 2,
          'right-far': activeCardIndex === 3
        }" @click="switchCard(0)">
          <div class="rotate-perspective mx-[10px]">
            <div class="mt-[10px] video-card-bg w-[3400px] h-[1080px] rotate-item2">
              <div class="w-full h-full py-[15px] box-border rotate-perspective">
                <div class="w-full text-center text-[28px] h-[40px] leading-[40px] card-title">
                  重点工程
                </div>
                <div class="major-project-controls-top">
                  <div class="major-project-tab-small"
                    :class="{ 'active': currentVideoComponent === 'internationalTab' }"
                    @click.stop="handleChangeTab('internationalTab')">国际工程</div>
                  <div class="major-project-tab-small" :class="{ 'active': currentVideoComponent === 'domesticTab' }"
                    @click.stop="handleChangeTab('domesticTab')">国内工程</div>
                </div>
                <component :is="currentVideoComponent" />
                <VideoGrid :projectList="projectList" v-if="currentVideoComponent === 'internationalTab'" type="major"
                  :loading="isLoadingMajorData" :itemsPerRow="4" @fullscreen="openFullscreenVideo" />
                <MonitorVideo :monitorData="monitorDataForMajor" v-if="currentVideoComponent == 'domesticTab'"
                  :loading="isLoadingMonitorDataForMajor" />

                <div v-else class="inactive-card-hint">
                  <div class="hint-icon">🏗️</div>
                  <div class="hint-text">点击查看重点工程项目</div>
                </div>
              </div>
            </div>
            <div v-if="activeCardIndex !== 0" class="card-overlay"></div>
          </div>
        </div>

        <!-- 国际工程卡片 -->
        <div class="carousel-card" :class="{
          'active': activeCardIndex === 1,
          'right': activeCardIndex === 0,
          'left': activeCardIndex === 2 || activeCardIndex === 3,
          'left-far': activeCardIndex === 3,
          'right-far': activeCardIndex === 0
        }" @click="switchCard(1)">
          <div class="rotate-perspective mx-[10px]">
            <div class="mt-[10px] video-card-bg w-[3400px] h-[1080px] rotate-item2">
              <div class="w-full h-full py-[15px] box-border rotate-perspective">
                <div class="w-full text-center text-[40px] h-[40px] leading-[40px] card-title">
                  国际工程
                </div>
                <!-- 国家选择器 - 右上角位置 -->
                <div class="country-selector">
                  <MySelect :options="countrySelectOptions" :selectValue="selectedCountryOption" @getOption="onCountrySelectOption" @resetSelect="onCountrySelectReset" />
                </div>
                <!-- 国际工程项目网格 - 按需渲染 -->
                <VideoGrid v-if="shouldRenderContent.internationalProjects" :projectList="internationalProjects" type="international" :loading="isLoadingInternationalData" :itemsPerRow="4" @fullscreen="openFullscreenVideo" />
                <div v-else class="inactive-card-hint">
                  <div class="hint-icon">🌏</div>
                  <div class="hint-text">请选择国家</div>
                </div>
              </div>
            </div>
            <div v-if="activeCardIndex !== 1" class="card-overlay"></div>
          </div>
        </div>

        <!-- 国内工程卡片 - 直接显示监控 -->
        <div class="carousel-card" :class="{
          'active': activeCardIndex === 2,
          'right': activeCardIndex === 0 || activeCardIndex === 1,
          'right-far': activeCardIndex === 0,
          'left': activeCardIndex === 3,
          'left-far': activeCardIndex === 0
        }" @click="switchCard(2)">
          <div class="rotate-perspective mx-[10px]">
            <div class="mt-[10px] video-card-bg w-[3400px] h-[1080px] rotate-item2">
              <div class="w-full h-full py-[15px] box-border rotate-perspective">
                <div class="w-full text-center text-[28px] h-[40px] leading-[40px] card-title">
                  国内工程监控
                </div>
                <!-- 监控视频网格 - 按需渲染 -->
                <div v-if="isLoadingMonitorData" class="loading-overlay">
                  <div class="loading-spinner"></div>
                  <p class="loading-text">正在加载监控数据...</p>
                </div>

                <MonitorVideo v-else-if="shouldRenderContent.domesticMonitoring" :monitorData="monitorData"
                  :loading="isLoadingMonitorData" />

                <!-- 如果没有数据显示空状态 -->
                <div v-else-if="monitorData.length === 0" class="empty-state">
                  <div class="empty-text">暂无监控设备</div>
                </div>
                <!-- </div> -->
                <!-- 非激活状态提示 -->
                <div v-else class="inactive-card-hint">
                  <div class="hint-icon">📹</div>
                  <div class="hint-text">点击查看国内工程监控</div>
                </div>
              </div>
            </div>
            <div v-if="activeCardIndex !== 2" class="card-overlay"></div>
          </div>
        </div>

        <!-- 公建中心卡片 -->
        <div class="carousel-card" :class="{
          'active': activeCardIndex === 3,
          'right': activeCardIndex === 0 || activeCardIndex === 1 || activeCardIndex === 2,
          'right-far': activeCardIndex === 1,
          'right-furthest': activeCardIndex === 0,
          'left': activeCardIndex === 0
        }" @click="switchCard(3)">
          <div class="rotate-perspective mx-[10px]">
            <div class="mt-[10px] video-card-bg w-[3400px] h-[1080px] rotate-item2">
              <div class="w-full h-full py-[15px] box-border rotate-perspective">
                <div class="w-full text-center text-[28px] h-[40px] leading-[40px] card-title">
                  公建中心
                </div>
                <div v-if="isLoadingPublicBuilding" class="loading-overlay">
                  <div class="loading-spinner"></div>
                  <p class="loading-text">正在加载监控数据...</p>
                </div>
                <GongJianZhongXin v-if="shouldRenderContent.publicBuilding" :deviceList="publicBuildingList" :loading="isLoadingPublicBuilding" />
                <div v-else class="inactive-card-hint">
                  <div class="hint-icon">🏢</div>
                  <div class="hint-text">点击查看公建中心项目</div>
                </div>
              </div>
            </div>
            <div v-if="activeCardIndex !== 3" class="card-overlay"></div>
          </div>
        </div>

        <div class="carousel-controls" style="display: none;">
          <div class="carousel-dot" :class="{ 'active': activeCardIndex === 0 }" @click="switchCard(0)"></div>
          <div class="carousel-dot" :class="{ 'active': activeCardIndex === 1 }" @click="switchCard(1)"></div>
          <div class="carousel-dot" :class="{ 'active': activeCardIndex === 2 }" @click="switchCard(2)"></div>
          <div class="carousel-dot" :class="{ 'active': activeCardIndex === 3 }" @click="switchCard(3)"></div>
        </div>

        <!-- 导航箭头 - 都在右侧 -->
        <div class="carousel-navigation">
          <!-- 左箭头 -->
          <div class="nav-arrow nav-arrow-left" @click="switchToPreviousCard">
            <div class="arrow-content">
              <div class="arrow-label">
                <span class="card-name">{{ getPreviousCardName() }}</span>
              </div>
              <div class="arrow-icon">
                <svg viewBox="0 0 24 24" width="64" height="64">
                  <path fill="currentColor" d="M15.41,16.58L10.83,12L15.41,7.42L14,6L8,12L14,18L15.41,16.58Z" />
                </svg>
              </div>
            </div>
          </div>

          <!-- 右箭头 -->
          <div class="nav-arrow nav-arrow-right" @click="switchToNextCard">
            <div class="arrow-content">
              <div class="arrow-label">
                <span class="card-name">{{ getNextCardName() }}</span>
              </div>
              <div class="arrow-icon">
                <svg viewBox="0 0 24 24" width="64" height="64">
                  <path fill="currentColor" d="M8.59,16.58L13.17,12L8.59,7.42L10,6L16,12L10,18L8.59,16.58Z" />
                </svg>
              </div>
            </div>
          </div>
        </div>

        <div class="base-navigation">
          <img src="@/assets/images/jingying/jingyingBase.png" @click="handleBaseClick" alt="底部导航" />
        </div>
      </div>

      <!-- 全屏视频弹窗 -->
      <div v-if="isFullscreenVisible" class="fullscreen-modal" @click="closeFullscreenVideo">
        <div class="fullscreen-content" @click.stop>

          <!-- 关闭按钮 -->
          <div class="fullscreen-close" @click="closeFullscreenVideo">
            <svg viewBox="0 0 24 24" width="32" height="32">
              <path fill="currentColor"
                d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" />
            </svg>
          </div>

          <!-- 视频信息 -->
          <div v-if="fullscreenProject" class="fullscreen-header">
            <h2 class="fullscreen-title">{{ getFullscreenDisplayName(fullscreenProject) }}</h2>
            <!-- <p class="fullscreen-subtitle">{{ fullscreenProject.xmmc }}</p> -->
          </div>

          <!-- 全屏播放器容器 -->
          <div class="fullscreen-player-wrapper">
            <!-- 加载状态 -->
            <div v-if="fullscreenLoading" class="fullscreen-loading">
              <div class="loading-spinner"></div>
              <p class="loading-text">正在加载视频...</p>
            </div>

            <!-- 错误状态 -->
            <div v-else-if="fullscreenError" class="fullscreen-error">
              <div class="error-icon">⚠️</div>
              <div class="error-text">{{ fullscreenError }}</div>
              <button class="retry-btn" @click="retryFullscreenVideo">重试</button>
            </div>

            <!-- 播放器容器 -->
            <div id="fullscreen-video-player" class="fullscreen-player">
            </div>
          </div>
        </div>
      </div>

    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, watch, nextTick } from "vue";
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import request from '@/utils/request';
// 导入优化后的管理器
import tokenManager from '@/utils/tokenManagers.js';
import playerManager from '@/utils/playerManagers.js';
import GongJianZhongXin from './components/GongJianZhongXin.vue';
import VideoGrid from './components/VideoGrid.vue';
import MonitorVideo from './components/MonitorVideo.vue';
import MySelect from './MySelect.vue';
// import { useIndex } from '@/assets/js/windowScale.js'; // 子页面不需要动态缩放

const activeCardIndex = ref(1);
let carouselTimer = null;

const router = useRouter();
const { t, locale } = useI18n();

// 卡片名称配置
const cardNames = ['重点工程', '国际工程', '国内工程监控', '公建中心'];

// 注释：子页面使用固定定位，不需要动态缩放
// const { style, setScale, getScale } = useIndex();

// 监控数据相关状态
const monitorData = ref([]);
const isLoadingMonitorData = ref(false);
const projectList = ref([])
const isLoadingMajorData = ref(false)

// 国际工程数据相关状态
const internationalProjects = ref([]);
const isLoadingInternationalData = ref(false);

// 播放器加载状态管理
const playerLoadingStates = ref(new Map());
const playerErrorStates = ref(new Map());

// 公建中心
const publicBuildingList = ref([])
const isLoadingPublicBuilding = ref(false)

// 全屏视频相关状态
const isFullscreenVisible = ref(false);
const fullscreenProject = ref(null);
const fullscreenLoading = ref(false);
const fullscreenError = ref(null);
const fullscreenPlayer = ref(null);

// Intersection Observer Refs
const internationalVideoGrid = ref(null);
const monitorVideoGrid = ref(null);
const internationalObserver = ref(null);
const monitorObserver = ref(null);

// 防抖工具函数
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func.apply(this, args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// 格式化时间到年月
// 格式化时间到年月
const formatTimeToYearMonth = (timeString) => {
  if (!timeString) return ''
  try {
    // 时间格式示例: "2025-04-23 21:34:27" 或 "2025-04-23"
    const date = new Date(timeString)
    
    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      console.error('无效的时间格式:', timeString)
      return ''
    }
    
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    return `${year}-${month}`
  } catch (error) {
    console.error('时间格式化失败:', error)
    return ''
  }
}

// 获取全屏显示的项目名称（对于国际工程和重点工程添加时间）
const getFullscreenDisplayName = (project) => {
  if (!project) return ''
  
  const baseName = project.xmjc || project.xmmc || project.storeName || project.name
  
  // 检查是否需要添加时间后缀
  // 1. 如果项目有 time 字段（通常是国际工程）
  if (project.time) {
    const timeFormat = formatTimeToYearMonth(project.time)
    return timeFormat ? `${baseName}-${timeFormat}` : baseName
  }
  
  // 2. 如果项目有 createTime 字段（通常是重点工程）
  if (project.createTime) {
    const timeFormat = formatTimeToYearMonth(project.createTime)
    return timeFormat ? `${baseName}-${timeFormat}` : baseName
  }
  
  return baseName
}

/**
 * 返回上一级 - 恢复进入视频页面前的状态
 */
const back = () => {
  console.log('现场视频页面返回上一级');

  // 获取进入视频页面前保存的状态
  const savedState = sessionStorage.getItem('beforeVideoState');
  if (savedState) {
    try {
      const state = JSON.parse(savedState);
      console.log('恢复保存的状态:', state);

      // 恢复所有保存的状态信息到sessionStorage和localStorage
      if (state.isChina) {
        localStorage.setItem("isChina", state.isChina);
      }
      if (state.clickCountry) {
        sessionStorage.setItem("clickCountry", state.clickCountry);
      }
      if (state.countryLevel) {
        sessionStorage.setItem("countryLevel", state.countryLevel);
      }
      if (state.countryTitle) {
        sessionStorage.setItem("countryTitle", state.countryTitle);
      }

      // 设置返回上一级标记，让业务布局页面使用handleBackFromVideo恢复状态
      sessionStorage.setItem("returnType", "backFromVideo");
      console.log('🔵 现场视频页面：设置returnType为backFromVideo');
      console.log('🔵 当前sessionStorage状态:', {
        returnType: sessionStorage.getItem("returnType"),
        countryLevel: state.countryLevel,
        countryTitle: state.countryTitle,
        clickCountry: state.clickCountry
      });

    } catch (error) {
      console.error('解析保存状态失败:', error);
      // 如果解析失败，使用默认的返回逻辑
      sessionStorage.setItem("returnType", "backToCountry");
    }
  } else {
    console.log('未找到保存的状态，使用默认返回逻辑');
    // 如果没有保存状态，使用默认的返回逻辑
    sessionStorage.setItem("returnType", "backToCountry");
  }

  // 向iframe发送"cancel"消息（返回上一级的特定消息）
  setTimeout(() => {
    const iframe = document.getElementById("iframe");
    if (iframe && iframe.contentWindow) {
      console.log('向iframe发送返回上一级消息: cancel');
      iframe.contentWindow.postMessage(
        { eve: "cancel" },
        "*"
      );
    }
  }, 100);

  // 跳转回业务布局首页
  router.replace({ path: "/business-display" });
};

/**
 * 返回首页 - 完全重置到业务展示页面的初始状态
 */
const backHome = () => {
  console.log('现场视频页面返回首页 - 完全重置状态');

  // 清除所有相关状态数据，确保返回到最初的页面状态
  localStorage.removeItem("isChina");
  localStorage.removeItem("clickProject");
  localStorage.removeItem("btnsActive");
  sessionStorage.removeItem("countryLevel");
  sessionStorage.removeItem("countryTitle");
  sessionStorage.removeItem("clickCountry");
  sessionStorage.removeItem("lastProjectState");
  sessionStorage.removeItem("clickProject");
  sessionStorage.removeItem("beforeVideoState");
  sessionStorage.removeItem("restoreFromVideo");

  // 设置返回首页标记，确保业务展示页面恢复到初始状态
  sessionStorage.setItem("shouldResetToInitial", "true");
  sessionStorage.setItem("returnType", "backHome");

  // 跳转到业务布局首页
  router.replace({ path: "/business-display" });
};

// 是否应该渲染内容（按需渲染优化）
const shouldRenderContent = ref({
  majorProjects: false,
  internationalProjects: false,
  domesticMonitoring: false,
  publicBuilding: false
});

// 计算属性：将显示的监控数据按行分组显示（每行4个）
const displayedMonitorRows = computed(() => {
  const rows = [];
  const itemsPerRow = 4;
  const monitors = monitorData.value;

  if (!monitors || monitors.length === 0) {
    return [];
  }

  // 按行分组
  for (let i = 0; i < monitors.length; i += itemsPerRow) {
    const row = monitors.slice(i, i + itemsPerRow);
    rows.push(row);
  }

  // 如果最后一行不满4个，用空白监控填充
  const lastRow = rows[rows.length - 1];
  if (lastRow && lastRow.length < itemsPerRow) {
    const placeholdersToAdd = itemsPerRow - lastRow.length;
    for (let i = 0; i < placeholdersToAdd; i++) {
      lastRow.push({
        id: `empty-monitor-${rows.length - 1}-${lastRow.length}`,
        isEmpty: true,
      });
    }
  }

  return rows;
});

// 计算属性：将显示的国际工程项目按行分组显示（每行4个）
const displayedInternationalRows = computed(() => {
  const rows = [];
  const itemsPerRow = 4;
  const projects = internationalProjects.value;

  if (!projects || projects.length === 0) {
    return [];
  }

  // 按行分组
  for (let i = 0; i < projects.length; i += itemsPerRow) {
    const row = projects.slice(i, i + itemsPerRow);
    rows.push(row);
  }

  // 如果最后一行不满4个，用空白项目填充
  const lastRow = rows[rows.length - 1];
  if (lastRow && lastRow.length < itemsPerRow) {
    const placeholdersToAdd = itemsPerRow - lastRow.length;
    for (let i = 0; i < placeholdersToAdd; i++) {
      lastRow.push({
        id: `empty-${rows.length - 1}-${lastRow.length}`,
        isEmpty: true
      });
    }
  }

  return rows;
});

// 获取国际工程项目列表
const fetchInternationalProjects = async (countryName) => {
  try {
    isLoadingInternationalData.value = true;
    // 在重新获取数据前，确保销毁所有现有播放器
    await playerManager.destroyAllPlayers('international');

    // 如果有国家参数，带上参数
    let response;
    if (countryName) {
      response = await request.get('/globalManage/zjmanage/largescreen/allsplist', { params: { countryName } });
    } else {
      response = await request.get('/globalManage/zjmanage/largescreen/allsplist');
    }

    if (response.code === 0 && response.data) {
      internationalProjects.value = response.data;
      console.log('✅ 国际工程项目获取成功:', internationalProjects.value.length, '个项目');
    } else {
      console.error('❌ 获取国际工程项目失败:', response.msg || '未知错误');
      internationalProjects.value = [];
    }
  } catch (error) {
    console.error('❌ 获取国际工程项目失败:', error);
    internationalProjects.value = [];
  } finally {
    isLoadingInternationalData.value = false;
  }
};

// 获取监控数据列表

// 1. 优化监控数据获取，确保完全重置
const fetchMonitorData = async () => {
  try {
    isLoadingMonitorData.value = true;
    console.log('🔄 开始获取国内监控数据...');
    
    // 🔥 关键修改：完全清理现有状态
    monitorData.value = [];
    playerLoadingStates.value.clear();
    playerErrorStates.value.clear();
    
    // 销毁所有现有播放器，等待完全清理
    await playerManager.destroyAllPlayers('monitor');
    console.log('🗑️ 现有播放器已清理');
    
    // 等待一段时间确保清理完成
    await new Promise(resolve => setTimeout(resolve, 300));

    const response = await request.get('/globalManage/zjmanage/largescreen/allcameraList');

    if (response.code === 0 && response.data) {
      const grouped = response.data.reduce((acc, item) => {
        if (!acc[item.storeName]) {
          acc[item.storeName] = item;
        }
        return acc;
      }, {});
      
      const newMonitorData = Object.values(grouped);
      console.log('✅ 监控数据获取成功:', newMonitorData.length, '个设备');
      
      // 🔥 确保数据完全更新
      monitorData.value = newMonitorData;
      
      // 等待DOM完全更新
      await nextTick();
      console.log('📺 监控数据DOM更新完成');
      
    } else {
      console.error('❌ 获取监控数据失败:', response.msg || '未知错误');
      monitorData.value = [];
    }
  } catch (error) {
    console.error('❌ 获取监控数据失败:', error);
    monitorData.value = [];
  } finally {
    isLoadingMonitorData.value = false;
    console.log('📊 监控数据获取流程结束');
  }
};
const monitorDataForMajor = ref([])
const isLoadingMonitorDataForMajor = ref(false)
// 获取重点工程监控数据列表
const fetchMonitorDataForMajor = async () => {
  try {
    isLoadingMonitorDataForMajor.value = true;
    // 在重新获取数据前，确保销毁所有现有播放器
    await playerManager.destroyAllPlayers('monitor');

    const response = await request.get('/globalManage/zjmanage/largescreen/allCameraImportList');

    if (response.code === 0 && response.data) {
      monitorDataForMajor.value = Array.isArray(response.data) ? response.data.slice(0, 1) : [];
      console.log('✅ 重点工程监控数据获取成功:', monitorDataForMajor.value.length, '个设备');
    } else {
      console.error('❌ 获取重点工程监控数据失败:', response.msg || '未知错误');
      monitorDataForMajor.value = [];
    }
  } catch (error) {
    console.error('❌ 获取重点工程监控数据失败:', error);
    monitorDataForMajor.value = [];
  } finally {
    isLoadingMonitorDataForMajor.value = false;
  }
};

// 播放器状态管理
const handlePlayerLoadingChange = (playerId, isLoading) => {
  if (isLoading) {
    playerLoadingStates.value.set(playerId, true);
  } else {
    playerLoadingStates.value.delete(playerId);
  }
};

const handlePlayerError = (playerId, error) => {
  playerErrorStates.value.set(playerId, error.message);
  console.error(`播放器 ${playerId} 错误:`, error);
};

// 手动清理播放器错误状态
const clearPlayerError = (playerId) => {
  playerErrorStates.value.delete(playerId);
  playerManager.clearErrorState(playerId);
  console.log(`🧹 手动清理播放器 ${playerId} 的错误状态`);
};

// 获取播放器管理器统计信息（调试用）
const getPlayerStats = () => {
  const stats = playerManager.getStats();
  console.log('📊 播放器统计信息:', stats);
  return stats;
};

// 打开全屏视频
const openFullscreenVideo = (project) => {
  // 检查项目是否有视频数据
  // 对于监控视频，检查deviceSerial；对于普通项目，检查sp
  const hasVideo = project.sp || project.deviceSerial;

  if (!hasVideo) {
    console.log('项目没有视频，无法打开全屏');
    return;
  }

  console.log('🖥️ 打开全屏视频:', project.xmjc || project.xmmc || project.storeName);
  console.log('🔍 全屏项目数据:', project);

  fullscreenProject.value = project;
  isFullscreenVisible.value = true;
  fullscreenLoading.value = true;
  fullscreenError.value = null;

  console.log('📱 全屏状态设置:', {
    isFullscreenVisible: isFullscreenVisible.value,
    fullscreenProject: fullscreenProject.value?.xmmc || fullscreenProject.value?.storeName,
    fullscreenLoading: fullscreenLoading.value
  });

  // 延迟初始化，确保DOM已渲染
  nextTick(() => {
    setTimeout(() => {
      debugFullscreenState(); // 调试状态
      initializeFullscreenPlayer();
    }, 100);
  });
};

// 关闭全屏视频
const closeFullscreenVideo = () => {
  console.log('❌ 关闭全屏视频');

  // 销毁全屏播放器
  if (fullscreenPlayer.value) {
    try {
      // 如果是监控视频，清理EZUIKit播放器
      if (fullscreenProject.value && fullscreenProject.value.deviceSerial && fullscreenProject.value.channelNo) {
        if (typeof fullscreenPlayer.value.stop === 'function') {
          fullscreenPlayer.value.stop();
        }
        if (typeof fullscreenPlayer.value.destroy === 'function') {
          fullscreenPlayer.value.destroy();
        }
      } else if (fullscreenPlayer.value.tagName === 'VIDEO') {
        // 如果是普通视频，清理video元素
        fullscreenPlayer.value.pause();
        fullscreenPlayer.value.src = '';
        if (fullscreenPlayer.value.parentNode) {
          fullscreenPlayer.value.parentNode.removeChild(fullscreenPlayer.value);
        }
      }
    } catch (error) {
      console.error('清理全屏播放器失败:', error);
    }
    fullscreenPlayer.value = null;
  }

  // 清空容器
  const container = document.getElementById('fullscreen-video-player');
  if (container) {
    container.innerHTML = '';
  }

  // 重置状态
  isFullscreenVisible.value = false;
  fullscreenProject.value = null;
  fullscreenLoading.value = false;
  fullscreenError.value = null;
};

// 重试全屏视频加载
const retryFullscreenVideo = () => {
  if (fullscreenProject.value) {
    console.log('🔄 重试加载全屏视频');

    // 先清理现有播放器
    if (fullscreenPlayer.value) {
      try {
        // 如果是监控视频，清理EZUIKit播放器
        if (fullscreenProject.value.deviceSerial && fullscreenProject.value.channelNo) {
          if (typeof fullscreenPlayer.value.stop === 'function') {
            fullscreenPlayer.value.stop();
          }
          if (typeof fullscreenPlayer.value.destroy === 'function') {
            fullscreenPlayer.value.destroy();
          }
        } else if (fullscreenPlayer.value.tagName === 'VIDEO') {
          // 如果是普通视频，清理video元素
          fullscreenPlayer.value.pause();
          fullscreenPlayer.value.src = '';
          if (fullscreenPlayer.value.parentNode) {
            fullscreenPlayer.value.parentNode.removeChild(fullscreenPlayer.value);
          }
        }
      } catch (error) {
        console.error('清理播放器失败:', error);
      }
      fullscreenPlayer.value = null;
    }

    // 清空容器
    const container = document.getElementById('fullscreen-video-player');
    if (container) {
      container.innerHTML = '';
    }

    // 重置状态并重新初始化
    fullscreenLoading.value = true;
    fullscreenError.value = null;

    setTimeout(() => {
      initializeFullscreenPlayer();
    }, 100);
  }
};

// 初始化全屏播放器
const initializeFullscreenPlayer = async () => {
  if (!fullscreenProject.value) return;

  const project = fullscreenProject.value;
  const container = document.getElementById('fullscreen-video-player');

  if (!container) {
    console.error('全屏播放器容器未找到');
    fullscreenError.value = '播放器容器初始化失败';
    fullscreenLoading.value = false;
    return;
  }

  try {
    // 清空容器
    container.innerHTML = '';

    // 判断是监控视频还是普通视频
    if (project.deviceSerial && project.channelNo) {
      // 监控视频 - 使用EZUIKit播放器
      console.log(`🎬 初始化全屏监控播放器:`, {
        storeName: project.storeName,
        deviceSerial: project.deviceSerial,
        channelNo: project.channelNo
      });

      // 直接使用EZUIKit初始化全屏播放器
      const accessToken = await tokenManager.getVideoToken();
      const ezuikitPlayer = new EZUIKit.EZUIKitPlayer({
        id: 'fullscreen-video-player',
        accessToken: accessToken,
        url: `ezopen://open.ys7.com/${project.deviceSerial}/${project.channelNo}.hd.live`,
        template: 'standard',
        plugin: ['talk'],
        audio: 1,
        autoplay: true,
        width: '100%',
        height: '100%'
      });

      // 监听播放器事件
      ezuikitPlayer.on('play', () => {
        console.log('▶️ 全屏监控视频开始播放');
        fullscreenLoading.value = false;
      });

      ezuikitPlayer.on('error', (error) => {
        console.error('❌ 全屏监控视频播放失败:', error);
        fullscreenError.value = `监控视频播放失败: ${error.message || '未知错误'}`;
        fullscreenLoading.value = false;
      });

      // 保存播放器引用
      fullscreenPlayer.value = ezuikitPlayer;

      console.log('✅ 全屏监控播放器初始化成功');

    } else if (project.sp) {
      // 普通视频 - 使用video元素
      const token = await tokenManager.getVideoToken();

      // 处理视频ID列表
      const videoIds = project.sp.split(',').filter(id => id.trim());
      const firstVideoId = videoIds[0];

      // 构建视频URL
      const userId = "941981453197164545";
      const videoUrl = `http://192.168.15.61:20600/papi/openapi/api/file/v2/common/download/${firstVideoId}?access_token=${token}&userid=${userId}`;

      console.log(`🎬 初始化全屏视频播放器:`, {
        projectName: project.xmjc,
        firstVideoId
      });

      // 创建video元素
      const videoElement = document.createElement('video');
      videoElement.src = videoUrl;
      videoElement.autoplay = true;
      videoElement.loop = true;
      videoElement.muted = false; // 全屏时允许声音
      videoElement.controls = true; // 全屏时显示控制条
      videoElement.className = 'fullscreen-video';
      videoElement.style.height = '100%';
      videoElement.style.objectFit = 'contain';
      videoElement.style.aspectRatio = '16/9';

      // 添加事件监听
      videoElement.addEventListener('loadstart', () => {
        console.log('📺 全屏视频开始加载');
      });

      videoElement.addEventListener('canplaythrough', () => {
        console.log('✅ 全屏视频可以播放');
        fullscreenLoading.value = false;
      });

      videoElement.addEventListener('playing', () => {
        console.log('▶️ 全屏视频开始播放');
        fullscreenLoading.value = false;
      });

      videoElement.addEventListener('error', (e) => {
        const errorMsg = `视频加载失败: ${e.target.error?.message || '未知错误'}`;
        console.error('❌ 全屏视频加载失败:', e.target.error);
        fullscreenError.value = errorMsg;
        fullscreenLoading.value = false;
      });

      // 添加视频元素
      container.appendChild(videoElement);

      // 保存播放器引用
      fullscreenPlayer.value = videoElement;

      console.log('✅ 全屏视频播放器初始化成功');
    } else {
      throw new Error('无效的视频数据格式');
    }

  } catch (error) {
    console.error('❌ 初始化全屏视频播放器失败:', error);
    fullscreenError.value = `初始化失败: ${error.message}`;
    fullscreenLoading.value = false;
  }
};

// 初始化单个监控播放器
// 2. 改进的播放器初始化函数
const initializeMonitorPlayer = async (monitor) => {
  const playerId = `monitor-player-${monitor.deviceSerial}-${monitor.channelNo}`;
  
  console.log(`🎬 开始初始化监控播放器: ${monitor.storeName} (${playerId})`);
  
  // 检查容器是否存在且有正确的尺寸
  const container = document.getElementById(playerId);
  if (!container) {
    console.error(`❌ 播放器容器不存在: ${playerId}`);
    return;
  }
  
  // 检查容器尺寸
  const containerRect = container.getBoundingClientRect();
  if (containerRect.width === 0 || containerRect.height === 0) {
    console.warn(`⚠️ 播放器容器尺寸为0: ${playerId}, 延迟初始化`);
    // 延迟重试
    setTimeout(() => {
      initializeMonitorPlayer(monitor);
    }, 500);
    return;
  }
  
  // 如果播放器已存在，先检查其状态
  if (playerManager.hasPlayer(playerId)) {
    console.log(`🔍 播放器 ${playerId} 已存在，检查状态...`);
    const playerInfo = playerManager.getPlayer(playerId);
    
    // 检查播放器是否正常工作
    if (playerInfo && playerInfo.instance) {
      // 尝试检查EZUIKit播放器状态
      const ezuikitElement = container.querySelector('.ezuikit-video-player');
      if (ezuikitElement) {
        console.log(`✅ 播放器 ${playerId} 状态正常，跳过初始化`);
        return;
      }
    }
    
    // 如果播放器状态异常，先销毁
    console.log(`🗑️ 播放器 ${playerId} 状态异常，重新初始化`);
    await playerManager.destroyPlayer(playerId);
    await new Promise(resolve => setTimeout(resolve, 200));
  }

  // 设置加载状态
  playerLoadingStates.value.set(playerId, true);
  playerErrorStates.value.delete(playerId); // 清除之前的错误状态
  
  try {
    // 初始化播放器
    await playerManager.initPlayer({
      data: monitor,
      type: 'monitor',
      onLoadingChange: (playerId, isLoading) => {
        console.log(`🔄 播放器 ${playerId} 加载状态: ${isLoading ? '加载中' : '完成'}`);
        handlePlayerLoadingChange(playerId, isLoading);
      },
      onError: (playerId, error) => {
        console.error(`❌ 播放器 ${playerId} 错误:`, error);
        handlePlayerError(playerId, error);
      },
      onReady: (playerId) => {
        console.log(`✅ 播放器 ${playerId} 就绪`);
        playerLoadingStates.value.delete(playerId);
      }
    });
    
    console.log(`✅ 监控播放器 ${playerId} 初始化成功`);
    
  } catch (error) {
    console.error(`❌ 初始化监控播放器失败 ${playerId}:`, error);
    playerLoadingStates.value.delete(playerId);
    playerErrorStates.value.set(playerId, error.message);
  }
};

// 初始化国际工程项目视频 - 改为懒加载观察者
const setupInternationalPlayerObserver = () => {
  if (internationalObserver.value) {
    internationalObserver.value.disconnect();
  }

  const options = {
    root: internationalVideoGrid.value,
    rootMargin: '0px 0px 200px 0px', // 预加载视口下方200px的视频
    threshold: 0.1,
  };

  internationalObserver.value = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        const target = entry.target;
        const projectId = target.dataset.projectId;
        if (!projectId) return;

        const project = internationalProjects.value.find((p) => p.id === projectId);

        if (project && project.sp) {
          const playerId = `international-player-${project.id}`;
          if (!playerManager.hasPlayer(playerId) && !playerLoadingStates.value.has(playerId)) {
            console.log(`👁️‍🗨️ [Lazy Load] Project in view, initializing: ${project.xmjc}`);
            initializeProjectVideo(project);
          }
        }
        internationalObserver.value.unobserve(target); // 触发后停止观察
      }
    });
  }, options);

  if (internationalVideoGrid.value) {
    const elementsToObserve = internationalVideoGrid.value.querySelectorAll('.video-item[data-project-id]');
    elementsToObserve.forEach((el) => {
      internationalObserver.value.observe(el);
    });
    console.log(`[Observer] Set up for ${elementsToObserve.length} international projects.`);
  }
};

// 防抖处理观察者回调
const debounceObserverCallback = debounce(function (entries) {
  let delay = 200; // 初始延迟
  entries.forEach((entry) => {
    if (entry.isIntersecting) {
      const target = entry.target;
      const monitorId = target.dataset.monitorId;
      if (!monitorId) return;

      const monitor = monitorData.value.find(
        (m) => !m.isEmpty && `${m.deviceSerial}-${m.channelNo}` === monitorId
      );

      if (monitor) {
        const playerId = `monitor-player-${monitor.deviceSerial}-${monitor.channelNo}`;
        if (!playerManager.hasPlayer(playerId) && !playerLoadingStates.value.has(playerId)) {
          console.log(`👁️‍🗨️ [Lazy Load] Monitor in view: ${monitor.storeName}, scheduling with delay ${delay}ms`);

          // 延迟初始化，并为下一个递增延迟
          setTimeout(() => {
            initializeMonitorPlayer(monitor);
          }, delay);
          delay += 1200; // 增加1.2秒的间隔
        }
      }
      monitorObserver.value.unobserve(target);
    }
  });
}, 300); // 300ms防抖

// 初始化国内监控视频 - 改为懒加载观察者
const setupMonitorPlayerObserver = () => {
  if (monitorObserver.value) {
    monitorObserver.value.disconnect();
  }

  const options = {
    root: monitorVideoGrid.value,
    rootMargin: '50px 0px 100px 0px', // 减小预加载范围
    threshold: 0.3, // 提高阈值，确保元素更可见时才触发
  };

  monitorObserver.value = new IntersectionObserver((entries) => {
    // 使用防抖处理，避免频繁触发
    debounceObserverCallback(entries);
  }, options);

  if (monitorVideoGrid.value) {
    const elementsToObserve = monitorVideoGrid.value.querySelectorAll('.video-item[data-monitor-id]');
    elementsToObserve.forEach((el) => {
      monitorObserver.value.observe(el);
    });
    console.log(`[Observer] Set up for ${elementsToObserve.length} domestic monitors.`);
  }
};

// 初始化单个项目视频播放器
const initializeProjectVideo = async (project) => {
  await playerManager.initProjectPlayer(
    project,
    handlePlayerLoadingChange,
    handlePlayerError
  );
};

// 获取状态样式类
const getStatusClass = (status) => {
  switch (status) {
    case '1': return 'online';
    case '0': return 'offline';
    case '-1': return 'error';
    default: return 'unknown';
  }
};

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case '1': return '在线';
    case '0': return '离线';
    case '-1': return '异常';
    default: return '未知';
  }
};

// 定期切换卡片的函数
function startCarouselTimer() {
  carouselTimer = setInterval(() => {
    activeCardIndex.value = (activeCardIndex.value + 1) % 4; // 现在有4张卡片
  }, 10000); // 每10秒切换一次
}

// 手动切换卡片
function switchCard(index) {
  if (activeCardIndex.value !== index) {
    console.log(`🔄 切换到卡片 ${index} (${cardNames[index]})`);

    // 断开当前卡片的观察者
    if (activeCardIndex.value === 1 && internationalObserver.value) {
      internationalObserver.value.disconnect();
      console.log('[Observer] Disconnected international project observer.');
    }
    if (activeCardIndex.value === 2 && monitorObserver.value) {
      monitorObserver.value.disconnect();
      console.log('[Observer] Disconnected domestic monitor observer.');
    }

    // 🔥 关键改动：只暂停非目标卡片的播放器，而不是销毁
    if (index !== 2) {
      // 如果不是切换到监控卡片，则暂停监控播放器
      playerManager.pausePlayersByType('monitor');
    }
    if (index !== 1) {
      // 如果不是切换到国际工程卡片，则暂停国际工程播放器
      playerManager.pausePlayersByType('international');
    }
    
    // 🏢 新增：如果切换到公建中心，确保其他类型播放器被暂停
    if (index === 3) {
      console.log('🏢 切换到公建中心，暂停其他播放器...');
      playerManager.pausePlayersByType('monitor');
      playerManager.pausePlayersByType('international');
      playerManager.pausePlayersByType('major');
    }

    // 更新激活状态
    activeCardIndex.value = index;

    // 更新渲染状态
    updateRenderState(index);
    console.log(`🔄 切换到卡片完成: ${cardNames[index]}`);

    // 重置自动切换计时器
    clearInterval(carouselTimer);
  }
}

// 切换到上一个卡片
function switchToPreviousCard() {
  const previousIndex = activeCardIndex.value === 0 ? 3 : activeCardIndex.value - 1;
  switchCard(previousIndex);
}

// 切换到下一个卡片  
function switchToNextCard() {
  const nextIndex = (activeCardIndex.value + 1) % 4;
  switchCard(nextIndex);
}

// 获取上一个卡片名称
function getPreviousCardName() {
  const previousIndex = activeCardIndex.value === 0 ? 3 : activeCardIndex.value - 1;
  return cardNames[previousIndex];
}

// 获取下一个卡片名称
function getNextCardName() {
  const nextIndex = (activeCardIndex.value + 1) % 4;
  return cardNames[nextIndex];
}

// 验证和修复播放器状态
// 3. 改进的状态验证和修复函数
async function validateAndFixPlayers() {
  console.log('🔍 验证监控播放器状态...');
  
  if (!monitorData.value || monitorData.value.length === 0) {
    console.log('⚠️ 没有监控数据，跳过验证');
    return;
  }

  const playersToCheck = [];
  const playersToFix = [];
  
  // 收集需要检查的播放器
  for (const monitor of monitorData.value) {
    if (monitor && !monitor.isEmpty && monitor.channelStatus === '1') {
      const playerId = `monitor-player-${monitor.deviceSerial}-${monitor.channelNo}`;
      playersToCheck.push({ playerId, monitor });
    }
  }
  
  console.log(`🔍 需要检查 ${playersToCheck.length} 个播放器`);
  
  // 检查每个播放器状态
  for (const { playerId, monitor } of playersToCheck) {
    const container = document.getElementById(playerId);
    
    if (!container) {
      console.log(`⚠️ 播放器容器不存在: ${playerId}`);
      continue;
    }
    
    // 检查容器是否在视口中且有正确尺寸
    const rect = container.getBoundingClientRect();
    const isVisible = rect.width > 0 && rect.height > 0;
    
    if (!isVisible) {
      console.log(`⚠️ 播放器容器不可见: ${playerId}`);
      continue;
    }
    
    // 检查播放器实例是否存在
    const hasPlayerInstance = playerManager.hasPlayer(playerId);
    const hasEZUIKitElement = container.querySelector('.ezuikit-video-player, video, canvas');
    
    if (!hasPlayerInstance || !hasEZUIKitElement) {
      console.log(`⚠️ 播放器 ${playerId} 需要修复 - 实例存在:${hasPlayerInstance}, DOM存在:${!!hasEZUIKitElement}`);
      playersToFix.push({ playerId, monitor });
    } else {
      // 检查播放器是否处于错误状态
      if (playerErrorStates.value.has(playerId)) {
        console.log(`⚠️ 播放器 ${playerId} 处于错误状态，需要修复`);
        playersToFix.push({ playerId, monitor });
      }
    }
  }

  // 修复有问题的播放器
  if (playersToFix.length > 0) {
    console.log(`🔧 开始修复 ${playersToFix.length} 个播放器`);
    
    for (let i = 0; i < playersToFix.length; i++) {
      const { playerId, monitor } = playersToFix[i];
      
      try {
        console.log(`🔧 修复播放器 ${playerId}...`);
        await fixPlayerState(playerId, monitor);
        
        // 为每个播放器间隔一定时间，避免同时初始化造成资源竞争
        if (i < playersToFix.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      } catch (error) {
        console.error(`❌ 修复播放器 ${playerId} 失败:`, error);
      }
    }
    
    console.log(`✅ 播放器修复完成`);
  } else {
    console.log(`✅ 所有播放器状态正常`);
  }
} 

// 修复单个播放器状态
async function fixPlayerState(playerId, monitor) {
  try {
    console.log(`🔧 开始修复播放器: ${playerId}`);
    
    // 清除错误状态
    playerErrorStates.value.delete(playerId);
    playerLoadingStates.value.delete(playerId);
    
    // 彻底销毁现有播放器
    if (playerManager.hasPlayer(playerId)) {
      await playerManager.destroyPlayer(playerId);
      console.log(`🗑️ 播放器 ${playerId} 已销毁`);
    }
    
    // 清空容器内容
    const container = document.getElementById(playerId);
    if (container) {
      container.innerHTML = '';
      console.log(`🧹 播放器容器 ${playerId} 已清空`);
    }
    
    // 等待清理完成
    await new Promise(resolve => setTimeout(resolve, 300));
    
    // 重新初始化
    await initializeMonitorPlayer(monitor);
    console.log(`✅ 播放器 ${playerId} 修复完成`);
    
  } catch (error) {
    console.error(`❌ 修复播放器 ${playerId} 失败:`, error);
    playerErrorStates.value.set(playerId, `修复失败: ${error.message}`);
  }
}
// 5. 改进的国内监控切换逻辑
const improvedDomesticMonitoringSwitch = async () => {
  console.log('🏠 切换到国内工程监控...');
  
  shouldRenderContent.value.domesticMonitoring = true;
  
  // 总是重新获取数据
  console.log('📊 重新获取监控数据...');
  await fetchMonitorData();
  
  // 等待数据和DOM完全更新
  console.log('⏱️ 等待DOM更新...');
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // 恢复已有的正常播放器
  console.log('▶️ 恢复播放器...');
  playerManager.resumePlayersByType('monitor');
  
  // 等待播放器恢复
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // 验证和修复播放器状态
  console.log('🔍 验证播放器状态...');
  await validateAndFixPlayers();
  
  // 最后设置观察者（如果需要的话）
  await nextTick();
  console.log('✅ 国内监控切换完成');
};
const fetchProjectList = async () => {
  try {
    isLoadingMajorData.value = true
    await playerManager.destroyAllPlayers('major')
    const res = await request.get('/globalManage/zjmanage/largescreen/allSpImportList')
    // debugger
    if (res && res.code === 0 && Array.isArray(res.data)) {
      projectList.value = res.data
      await nextTick()
      playerManager.resumePlayersByType('major');
      // setupMajorPlayerObserver()
    } else {
      projectList.value = []
    }
  } catch (e) {
    console.log(e)
    projectList.value = []
  } finally {
    isLoadingMajorData.value = false
  }
}
// 获取设备列表并按 projectId 分组，每组只取第一个
const fetchPublicBuildingList = async () => {
  try {
    isLoadingPublicBuilding.value = true
    console.log('🔍 开始获取公建中心设备列表...')
    
    // 🔄 清空现有数据，确保重新加载
    publicBuildingList.value = []
    
    const res = await request.post('/globalManage/projectCamera/publicPageList', {
      pageNo: 1,
      pageSize: 100
    })

    console.log('📡 公建中心API响应:', res)

    if (res && res.code === 0 && res.data && Array.isArray(res.data.list)) {
      const rawList = res.data.list
      console.log('📋 原始设备列表:', rawList.length, '个设备')
      
      const map = new Map()
      rawList.forEach(item => {
        if (item.projectId && !map.has(item.projectId)) {
          map.set(item.projectId, item)
        }
      })
      
      // 🔄 确保数据完全更新
      const newDeviceList = Array.from(map.values())
      publicBuildingList.value = newDeviceList
      
      console.log('✅ 公建中心设备去重后:', publicBuildingList.value.length, '个设备')
      console.log('📋 设备详情:', publicBuildingList.value)
      
      // 🎯 触发响应式更新，确保子组件能接收到新数据
      await nextTick()
      console.log('🔄 公建中心数据响应式更新完成')
      
    } else {
      console.error('❌ 公建中心API响应异常:', res.msg || '数据格式不正确')
      publicBuildingList.value = []
    }
  } catch (e) {
    console.error('❌ 获取公建中心设备列表失败:', e)
    publicBuildingList.value = []
  } finally {
    isLoadingPublicBuilding.value = false
    console.log('📊 公建中心数据获取流程结束')
  }
}

// 更新渲染状态
// 更新渲染状态
const updateRenderState = async (cardIndex) => {
  // 根据当前卡片设置渲染状态并加载数据
  switch (cardIndex) {
    case 0: // 重点项目   
      handleChangeTab('internationalTab');
      break;

    case 1: // 国际工程
      shouldRenderContent.value.internationalProjects = true;
      if (countryList.value.length === 0) {
        await fetchInternationalCountryList();
      }
      if (internationalProjects.value.length === 0) {
        await fetchInternationalProjects();
      }
      await nextTick();
      playerManager.resumePlayersByType('international');
      break;

    case 2: // 国内监控
      console.log('🏠 切换到国内工程监控...');
      shouldRenderContent.value.domesticMonitoring = true;
      
      // 🔥 总是重新获取数据，确保完全刷新
      console.log('📊 重新获取监控数据...');
      await fetchMonitorData();
      
      // 🔥 等待数据和DOM充分更新，避免黑屏
      console.log('⏱️ 等待DOM完全更新...');
      await new Promise(resolve => setTimeout(resolve, 1200));
      
      // 🔥 分阶段恢复播放器，避免同时初始化
      console.log('▶️ 分阶段恢复播放器...');
      playerManager.resumePlayersByType('monitor');
      
      // 🔥 给播放器足够时间启动
      await new Promise(resolve => setTimeout(resolve, 800));
      
      // 🔥 验证和修复播放器状态
      console.log('🔍 验证和修复播放器状态...');
      await validateAndFixPlayers();
      
      // 🔥 最终确认和清理
      await nextTick();
      console.log('✅ 国内监控切换完成，当前设备数:', monitorData.value.length);
      break;

    case 3: // 公建中心
      console.log('🏢 开始切换到公建中心卡片...');
      shouldRenderContent.value.publicBuilding = true;
      
      // 总是重新获取数据，确保数据最新
      console.log('📊 重新获取公建中心数据...');
      await fetchPublicBuildingList();
      
      // 等待数据处理完成
      await new Promise(resolve => setTimeout(resolve, 800));
      
      console.log('🎬 公建中心数据加载完成，准备初始化播放器...');
      
      // 等待DOM更新
      await nextTick();
      
      // 给公建中心组件一些时间来初始化其内部的播放器
      await new Promise(resolve => setTimeout(resolve, 500));
      
      console.log('✅ 公建中心卡片切换完成:', publicBuildingList.value.length, '个设备');
      break;
  }
};

// 安全重绘单个播放器
async function safeRepaintPlayer(playerId, container) {
  try {
    // 方法1: 强制重排
    container.style.transform = 'translateZ(0)';
    // 强制浏览器重排
    void container.offsetHeight;

    await new Promise(resolve => {
      requestAnimationFrame(() => {
        container.style.transform = '';
        resolve();
      });
    });

    // 方法2: 检查播放器状态并恢复 (对EZUIKit可能不直接适用)
    const playerInfo = playerManager.getPlayer(playerId);
    if (playerInfo && playerInfo.instance) {
      const player = playerInfo.instance;
      // EZUIKit没有标准的play/pause/readyState API，但可以尝试调用其内部方法
      if (typeof player.play === 'function') {
        try {
          await player.play();
        } catch (error) {
          // 很多时候play()会因用户未交互而失败，这里静默处理
        }
      }
    }
  } catch (error) {
    console.error(`重绘播放器 ${playerId} 失败:`, error);
  }
}

// 重绘所有监控播放器
const repaintAllMonitorPlayers = async () => {
  console.log('[Repaint Fix] 开始重绘所有监控播放器...');

  // 暂停DOM观察，避免重绘时触发不必要的检测
  if (playerManager.domObserver) {
    playerManager.domObserver.disconnect();
  }

  await new Promise(resolve => setTimeout(resolve, 100));

  const repaintPromises = [];

  for (const monitor of monitorData.value) {
    if (monitor && !monitor.isEmpty && monitor.channelStatus === '1') {
      const playerId = `monitor-player-${monitor.deviceSerial}-${monitor.channelNo}`;
      const playerContainer = document.getElementById(playerId);

      if (playerContainer && playerManager.hasPlayer(playerId)) {
        repaintPromises.push(safeRepaintPlayer(playerId, playerContainer));
      }
    }
  }

  await Promise.all(repaintPromises);

  // 重新启动DOM观察
  if (playerManager.domObserver) {
    playerManager.domObserver.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  console.log(`[Repaint Fix] 重绘完成`);
};

// 处理底部导航图片点击
function handleBaseClick() {
  // 切换到下一张卡片
  // activeCardIndex.value = (activeCardIndex.value + 1) % 4;
  switchCard((activeCardIndex.value + 1) % 4)
  // 重置自动切换计时器
  clearInterval(carouselTimer);
  // startCarouselTimer(); // 如果需要重新启动自动切换，取消注释
}



const currentVideoComponent = ref('internationalTab')

// 调试函数
const debugFullscreenState = () => {
  console.log('🔍 全屏状态调试信息:', {
    isFullscreenVisible: isFullscreenVisible.value,
    fullscreenProject: fullscreenProject.value,
    fullscreenLoading: fullscreenLoading.value,
    fullscreenError: fullscreenError.value,
    fullscreenPlayer: fullscreenPlayer.value,
    DOM_container: document.getElementById('fullscreen-video-player')
  });
  // 检查DOM元素是否存在
  const modal = document.querySelector('.fullscreen-modal');
  const content = document.querySelector('.fullscreen-content');
  console.log('🔍 DOM元素检查:', {
    modal_exists: !!modal,
    modal_visible: modal ? getComputedStyle(modal).display : 'not found',
    content_exists: !!content,
    content_visible: content ? getComputedStyle(content).display : 'not found'
  });
};

// 暴露调试函数到全局，方便控制台调用
if (typeof window !== 'undefined') {
  window.debugFullscreenState = debugFullscreenState;
}

async function handleChangeTab(tab) {
  switch (tab) {
    case 'internationalTab':
      if (projectList.value.length === 0) {
        fetchProjectList();
      }
      currentVideoComponent.value = 'internationalTab'
      break;
    case 'domesticTab':
      currentVideoComponent.value = 'domesticTab'
      if (monitorDataForMajor.value.length === 0) {
        fetchMonitorDataForMajor();
      }
      // 关键修改：减少等待时间，并分步骤处理
      await new Promise(resolve => setTimeout(resolve, 800));
      // 先恢复已有播放器
      playerManager.resumePlayersByType('monitor');
      // 检查并修复播放器状态
      await validateAndFixPlayers();
      // 再设置懒加载观察者
      await nextTick();
      setupMonitorPlayerObserver();
  }
}

// 添加语言切换函数
function toggleLanguage() {
  const currentLang = locale.value;
  const newLang = currentLang === 'zh' ? 'en' : 'zh';
  locale.value = newLang;
  localStorage.setItem('language', newLang);
}

// 在组件卸载时清除定时器
onUnmounted(async () => {
  console.log('🧹 组件卸载，开始清理资源...');

  clearInterval(carouselTimer);

  // 断开观察者
  if (internationalObserver.value) {
    internationalObserver.value.disconnect();
  }
  if (monitorObserver.value) {
    monitorObserver.value.disconnect();
  }

  // 清理DOM观察者
  if (playerManager.domObserver) {
    playerManager.domObserver.disconnect();
  }

  // 清理所有播放器
  await playerManager.destroyAllPlayers();

  // 清理全屏播放器
  if (fullscreenPlayer.value) {
    try {
      fullscreenPlayer.value.pause();
      fullscreenPlayer.value.src = '';
      if (fullscreenPlayer.value.parentNode) {
        fullscreenPlayer.value.parentNode.removeChild(fullscreenPlayer.value);
      }
    } catch (error) {
      console.error('清理全屏播放器失败:', error);
    }
    fullscreenPlayer.value = null;
  }

  // 清理Token管理器
  tokenManager.cleanup();

  // 移除键盘事件监听器
  document.removeEventListener('keydown', handleKeydown);

  console.log('✅ 资源清理完成');
});

onMounted(async () => {
  console.log('🚀 组件挂载，初始化页面...');
  console.log('📋 初始状态 - activeCardIndex:', activeCardIndex.value);
  console.log('📋 初始状态 - shouldRenderContent:', shouldRenderContent.value);

  // 激活当前卡片的渲染状态并加载数据
  await updateRenderState(activeCardIndex.value);

  console.log('📋 updateRenderState后 - shouldRenderContent:', shouldRenderContent.value);

  // 启动DOM观察
  if (playerManager.domObserver) {
    playerManager.domObserver.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeydown);

  console.log('✅ 页面初始化完成');
});

// 键盘事件处理
const handleKeydown = (event) => {
  if (event.key === 'Escape' && isFullscreenVisible.value) {
    closeFullscreenVideo();
  }
};

const countryList = ref([]);
const isLoadingCountryList = ref(false);

// 获取国际工程国家列表
const fetchInternationalCountryList = async () => {
  try {
    isLoadingCountryList.value = true;
    const response = await request.get('/globalManage/zjmanage/largescreen/allSpCountrylist');
    if (response.code === 0 && response.data) {
      countryList.value = response.data;
      console.log('✅ 国际工程国家列表获取成功:', countryList.value.length, '个国家');
    } else {
      console.error('❌ 获取国际工程国家列表失败:', response.msg || '未知错误');
      countryList.value = [];
    }
  } catch (error) {
    console.error('❌ 获取国际工程国家列表失败:', error);
    countryList.value = [];
  } finally {
    isLoadingCountryList.value = false;
  }
}

const selectedCountry = ref('');
const countrySelectOptions = computed(() => [
  { id: '', name: '请选择国家' },
  ...countryList.value.map(c => ({ id: c.countryName, name: c.countryName }))
]);
const selectedCountryOption = computed(() => {
  return countrySelectOptions.value.find(opt => opt.name === selectedCountry.value) || countrySelectOptions.value[0];
});
function onCountrySelectOption(option) {
  selectedCountry.value = option.name;
  if (option.id === '') {
    // 请选择国家，查全部
    fetchInternationalProjects();
  } else {
    // 查具体国家
    fetchInternationalProjects(option.name);
  }
  shouldRenderContent.value.internationalProjects = true;
}

function onCountrySelectReset() {
  selectedCountry.value = '';
  fetchInternationalProjects(); // 重置后查询全部数据
  shouldRenderContent.value.internationalProjects = true;
}

// 下拉显示时动态设置选项宽度
function onCountrySelectVisibleChange(visible) {
  if (!visible) return
  nextTick(() => {
    const el = countrySelectRef.value?.$el
    if (el) {
      const width = `${el.offsetWidth}px`
      selectOptionWidth.value = width
      // 同步 popper 面板宽度
      const popperEl = el.querySelector('.country-select-popper')
      if (popperEl) {
        popperEl.style.width = width
      }
    }
  })
}
</script>


<style lang="scss" scoped>
/* 视频展示容器样式 - 参考projectDetail页面布局 */
.video-display-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: #0a0f1a;
  z-index: 2;
}

/* 简化头部样式 - 保持原有设计 */
.simple-header {
  width: 100%;
  height: 122px;
  position: fixed;
  top: 0px;
  left: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-center {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.title-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.title-bg {
  height: 110px;
}

/* 返回首页按钮样式 - 参考projectDetail */
.backHome-button {
  position: absolute;
  top: 25px;
  left: 60px;
  display: flex;
  align-items: center;
  gap: 8px;
  pointer-events: auto;
  cursor: pointer;
  z-index: 10;
  transition: all 0.3s ease;
  font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 16px;
  text-shadow: 0px 0px 4px #0085FF;
  color: white;
  text-align: right;
  font-style: normal;
  text-transform: none;

  img {
    width: 18px;
    height: 18px;
  }
}

/* 返回上一级按钮样式 - 参考projectDetail */
.back-button {
  position: absolute;
  top: 25px;
  left: 170px;
  display: flex;
  align-items: center;
  gap: 8px;
  pointer-events: auto;
  cursor: pointer;
  z-index: 10;
  transition: all 0.3s ease;
  font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 16px;
  text-shadow: 0px 0px 4px #0085FF;
  color: white;
  text-align: right;
  font-style: normal;
  text-transform: none;
}

/* 主内容区域样式 */
.main-content-area {
  width: 100%;
  height: calc(100% - 122px);
  /* 为头部留出空间 */
  position: absolute;
  top: 122px;
  left: 0;
  pointer-events: all;
  background-image: url('@/assets/images/jingying/jingyingBg.png');
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  z-index: 1;

  .video-card-bg {
    background: url("@/assets/images/xianchangVideo/bgDp.png") no-repeat center center / 100% 100%;
  }
}

/* 视频卡片容器样式 */
.video-cards-container {
  position: relative;
  width: 100%;
  height: 100%;
  perspective: 2000px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

/* 轮播卡片样式 */
.carousel-card {
  position: absolute;
  transition: all 0.8s cubic-bezier(0.25, 0.1, 0.25, 1);
  cursor: pointer;
  transform-style: preserve-3d;
  top: 60px;

  &.active {
    z-index: 10;
    transform: translateZ(100px) scale(1.05);
    filter: brightness(1.1);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
  }

  &.left {
    transform: translateX(-2980px) translateZ(-385px) translateY(-25px) rotateY(-25deg) scale(0.9);
    filter: brightness(0.7) blur(2px);
    opacity: 0.75;
    z-index: 4;
    zoom: 1.06;
  }

  &.right {
    transform: translateX(3010px) translateZ(-385px) rotateY(25deg) scale(0.9);
    filter: brightness(0.7) blur(2px);
    opacity: 0.75;
    z-index: 4;
    zoom: 1.04;
  }

  &.right-far {
    transform: translateX(2600px) translateZ(-401px) rotateY(30deg) translateY(-62px) scale(0.8);
    filter: brightness(0.6) blur(3px);
    opacity: 0.6;
    z-index: 3;
    zoom: 1.15;
  }

  &.right-furthest {
    transform: translateX(-2259px) translateZ(-150px) translateY(-50px) rotateY(149deg) scale(0.7);
    filter: brightness(0.5) blur(4px);
    opacity: 0.45;
    z-index: 2;
    zoom: 1.16;
  }

  &.left-far {
    transform: translateX(-2880px) translateZ(-385px) rotateY(-35deg) scale(0.8);
    filter: brightness(0.6) blur(3px);
    opacity: 0.6;
    z-index: 3;
  }

  &.left-furthest {
    transform: translateX(-3950px) translateZ(-450px) rotateY(-40deg) scale(0.7);
    filter: brightness(0.5) blur(4px);
    opacity: 0.45;
    z-index: 2;
  }
}

/* 轮播控制器样式 */
.carousel-controls {
  position: absolute;
  bottom: -40px;
  display: flex;
  justify-content: center;
  gap: 15px;
  z-index: 20;

  .carousel-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;

    &.active {
      background-color: #fff;
      transform: scale(1.2);
    }
  }
}

/* 旋转透视样式 */
.rotate-perspective {
  perspective: 800px;

  .card-title {
    width: 100%;
    margin-top: 10px;
    font-family: Weiruanyahei, Weiruanyahei;
    font-weight: 500;
    font-size: 36px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    text-align: center;
  }
}

/* 重点工程切换按钮样式 - 左上角位置 */
.major-project-controls-top {
  position: absolute;
  top: 32px;
  left: 30px;
  display: flex;
  gap: 10px;
  z-index: 10;
}

.major-project-tab-small {
  width: 120px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: Alibaba PuHuiTi 2.0, sans-serif;
  font-weight: 500;
  font-size: 14px;
  color: #FFFFFF;
  user-select: none;

  /* 未选中状态 */
  background: rgba(70, 118, 226, 0.15);
  box-shadow: inset 0px 0px 8px 1px rgba(70, 118, 226, 0.5);
  border: 1px solid rgba(70, 118, 226, 0.4);

  &:hover {
    background: rgba(70, 118, 226, 0.25);
    border-color: rgba(70, 118, 226, 0.6);
    transform: translateY(-1px);
  }

  /* 选中状态 */
  &.active {
    background: rgba(70, 118, 226, 0.3);
    box-shadow: inset 0px 0px 8px 1px #4676E2, inset 0px 0px 8px 1px #4676E2;
    border: 1px solid #4676E2;
    color: #00FFFF;
    font-weight: 600;

    &:hover {
      transform: none;
    }
  }
}

/* 重点工程网格特殊样式 */
.major-projects-grid {
  margin-top: 0px;
}

/* 国家选择器样式 */
.country-selector {
  position: absolute;
  top: 30px;
  right: 30px;
  z-index: 15;
  width: 300px;
  /* 固定容器宽度 */
}

.custom-country-select {
  /* 固定选择器宽度，保证宽度同步 */
  width: 220px !important;
  --el-select-border-color: #00cfff;
  --el-select-hover-border-color: #5eaaff;
  --el-select-bg-color: rgba(10, 30, 60, 0.95);
  --el-select-input-color: #00ffff;
  --el-select-placeholder-color: #fff;
  --el-select-dropdown-bg-color: #112244;
  --el-select-dropdown-hover-bg-color: #223366;
  --el-select-dropdown-item-color: #00ffff;
  --el-select-dropdown-item-hover-color: #fff;
  --el-select-clear-hover-color: #5eaaff;
}

:deep(.custom-country-select .el-select__wrapper) {
  background: rgba(29, 64, 104) !important;
}

:deep(.custom-country-select .el-select__placeholder) {
  color: var(--el-select-placeholder-color) !important;
}

:deep(.custom-country-select .el-input__wrapper) {
  border-radius: 8px;
  border: 2px solid var(--el-select-border-color);
  background: var(--el-select-bg-color);
  box-shadow: 0 0 8px #00cfff55;
  transition: border 0.2s, box-shadow 0.2s;
}

:deep(.custom-country-select .el-input__inner) {
  color: var(--el-select-input-color);
  font-weight: 500;
  background: transparent;
  letter-spacing: 2px;
}

:deep(.custom-country-select .el-input__wrapper:hover),
:deep(.custom-country-select .el-input__wrapper.is-focus) {
  border-color: var(--el-select-hover-border-color);
  box-shadow: 0 0 12px #5eaaff99;
}

:deep(.custom-country-select .el-input__suffix) {
  color: var(--el-select-input-color);
}

:deep(.custom-country-select .el-select-dropdown) {
  background: var(--el-select-dropdown-bg-color) !important;
  border-radius: 8px;
  box-shadow: 0 4px 24px #00cfff33;
}

/* 全局样式 */
:global(.el-select__popper.el-popper),
:global(.el-select__popper.el-popper .el-select-dropdown) {
  color: #fff;
  background: rgba(29, 64, 104, 1) !important;
}

:global(.el-select-dropdown__item) {
  color: #fff;
  font-weight: 500;
  border-radius: 4px;
  margin: 2px 4px;
  transition: background 0.2s, color 0.2s;
  background: rgba(29, 64, 104, 1) !important;
}

:global(.el-select-dropdown__item:hover) {
  background: rgba(40, 90, 160, 1) !important;
  color: #00ffff !important;
}

:global(.el-select-dropdown__item.selected) {
  background: rgba(0, 191, 255, 0.18) !important;
  color: #00ffff !important;
}

.video-grid {
  width: 100%;
  height: calc(100% - 80px);
  padding: 50px;
  display: flex;
  flex-direction: column;
  gap: 15px;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;

  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;

    &:hover {
      background: rgba(255, 255, 255, 0.5);
    }
  }
}

.video-row {
  height: 270px;
  display: flex;
  gap: 15px;
  flex-shrink: 0;
}

.video-item {
  flex: 1;
  aspect-ratio: 16/9;
  min-height: 140px;
  border-radius: 8px;
  overflow: hidden;
}

.video-bg {
  width: 100%;
  height: 100%;
  display: block;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  padding: 0;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.02);
    box-shadow: 0 4px 12px rgba(0, 255, 255, 0.3);
  }
}

.project-name {
  position: absolute;
  bottom: -10px;
  right: 0;
  z-index: 5;
  width: 50%;
  height: auto;
  line-height: 1.4;
  padding: 8px 12px;
  box-sizing: border-box;
  // background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0) 100%);
  display: block;
  font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 24px;
  color: #00FFFF;
  text-align: right;
  font-style: normal;
  text-transform: none;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.video-player {
  position: absolute;
  top: 0;
  left: 0;
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
  border-radius: 6px;
  max-width: 100% !important;
  max-height: 100% !important;
  box-sizing: border-box !important;
}

.base-navigation {
  position: absolute;
  bottom: 50px;
  width: 100%;
  display: flex;
  justify-content: center;
  z-index: 20;

  img {
    cursor: pointer;
    width: 60%;
    max-width: 800px;
  }
}

// 加载状态样式
.loading-overlay {
  position: absolute;
  top: 80px;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #00FFFF;
  animation: spin 1s ease-in-out infinite;
}

.loading-text {
  margin-top: 15px;
  color: #00FFFF;
  font-size: 16px;
  font-weight: 500;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// 空状态样式
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: rgba(255, 255, 255, 0.6);
}

.empty-text {
  font-size: 18px;
  font-weight: 500;
}

// 加载更多样式
.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #00FFFF;
  margin-top: 10px;
}

.loading-spinner-small {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(0, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #00FFFF;
  animation: spin 1s ease-in-out infinite;
  margin-right: 10px;
}

.loading-more-text {
  font-size: 14px;
  font-weight: 500;
}

// 没有更多数据样式
.no-more-data {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: rgba(255, 255, 255, 0.6);
  margin-top: 10px;
}

.no-more-text {
  font-size: 14px;
  font-weight: 500;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 15px;
  text-align: center;
  width: 100%;
}

// 空白项目样式
.empty-project {
  opacity: 0.3;
  pointer-events: none;
}

.empty-video-placeholder {
  width: 200px;
  height: 120px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed rgba(255, 255, 255, 0.2);
}

.placeholder-icon {
  font-size: 30px;
  opacity: 0.5;
}

/* 国际工程项目样式 */
.video-player-container {
  width: 100%;
  height: 100%;
  background: #000;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.no-video-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2px dashed rgba(255, 255, 255, 0.3);
}

.placeholder-text {
  color: rgba(255, 255, 255, 0.6);
  font-size: 11px;
  text-align: center;
  margin-top: 4px;
}

/* 监控背景样式 */
.monitor-bg {
  width: 100%;
  height: 100%;
  display: block;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  padding: 0;
  transition: all 0.3s ease;
  object-fit: cover;
  /* Ensure video fits within the container */
}

.monitor-bg:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 255, 255, 0.3);
}

.monitor-name {
  position: absolute;
  bottom: 20px;
  right: 0;
  z-index: 5;
  width: 50%;
  height: auto;
  padding: 8px 12px;
  box-sizing: border-box;
  // background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0) 100%);
  display: block;
  font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 24px;
  color: #00FFFF;
  text-align: right;
  font-style: normal;
  text-transform: none;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.monitor-info {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 4px;
  font-size: 10px;
  width: 100%;
  justify-content: center;
  flex-wrap: wrap;
}

.device-serial,
.channel-no {
  background: rgba(0, 0, 0, 0.5);
  color: rgba(255, 255, 255, 0.8);
  padding: 1px 4px;
  border-radius: 2px;
  font-size: 9px;
}

.status-indicator {
  padding: 1px 4px;
  border-radius: 8px;
  font-size: 9px;
  font-weight: 500;
}

.status-indicator.online {
  background: rgba(76, 175, 80, 0.3);
  color: #4CAF50;
}

.status-indicator.offline {
  background: rgba(255, 152, 0, 0.3);
  color: #FF9800;
}

.status-indicator.error {
  background: rgba(244, 67, 54, 0.3);
  color: #F44336;
}

.status-indicator.unknown {
  background: rgba(158, 158, 158, 0.3);
  color: #9E9E9E;
}

/* 监控播放器容器 */
.monitor-player {
  width: 100%;
  height: 100%;
  background: #000;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
  aspect-ratio: 16/9;
  /* Maintain aspect ratio */
}

.monitor-player-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-player-container-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* 离线监控显示 */
.offline-monitor {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2px dashed rgba(255, 255, 255, 0.3);
}

.offline-icon {
  font-size: 24px;
  opacity: 0.5;
  margin-bottom: 4px;
}

.offline-text {
  color: rgba(255, 255, 255, 0.6);
  font-size: 11px;
  text-align: center;
}

/* 空白监控样式 */
.empty-monitor {
  opacity: 0.3;
  pointer-events: none;
}

.empty-monitor-placeholder {
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed rgba(255, 255, 255, 0.2);
  flex-shrink: 0;
}

/* 监控播放器EZUIKit样式优化 */
:deep(.monitor-player .ez-player) {
  border-radius: 4px !important;
  overflow: hidden !important;
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  max-height: 100% !important;
  box-sizing: border-box !important;
  position: relative !important;
}

:deep(.monitor-player .ez-player-container) {
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  max-height: 100% !important;
  box-sizing: border-box !important;
}

:deep(.monitor-player .ez-player-video) {
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  max-height: 100% !important;
  object-fit: cover !important;
  box-sizing: border-box !important;
}

/* 强制限制所有EZUIKit相关元素的尺寸 */
:deep(.monitor-player .ezuikit-video-player) {
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  max-height: 100% !important;
}

:deep(.monitor-player .ezuikit-video-player *) {
  max-width: 100% !important;
  max-height: 100% !important;
  box-sizing: border-box !important;
}

/* EZUIKit播放器样式优化 */
:deep(.ez-player) {
  border-radius: 6px !important;
  overflow: hidden !important;
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  max-height: 100% !important;
  box-sizing: border-box !important;
  position: relative !important;
}

:deep(.ez-player-container) {
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  max-height: 100% !important;
  box-sizing: border-box !important;
}

:deep(.ez-player-video) {
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  max-height: 100% !important;
  object-fit: cover !important;
  box-sizing: border-box !important;
}

/* 强制限制所有EZUIKit相关元素的尺寸 */
:deep(.ezuikit-video-player) {
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  max-height: 100% !important;
}

:deep(.ezuikit-video-player *) {
  max-width: 100% !important;
  max-height: 100% !important;
  box-sizing: border-box !important;
}

/* 隐藏滚动条但保留滚动功能 */
.popup-body::-webkit-scrollbar,
.camera-list::-webkit-scrollbar {
  width: 6px;
}

.popup-body::-webkit-scrollbar-track,
.camera-list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.popup-body::-webkit-scrollbar-thumb,
.camera-list::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.popup-body::-webkit-scrollbar-thumb:hover,
.camera-list::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 播放器状态覆盖层 */
.player-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: 4px;
}

.player-error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(40, 44, 52, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: 4px;
  border: 1px solid rgba(244, 67, 54, 0.3);
}

.loading-text-small {
  color: #00FFFF;
  font-size: 10px;
  font-weight: 500;
  margin-top: 4px;
  text-align: center;
}

.error-text-small {
  color: #FF6B6B;
  font-size: 10px;
  font-weight: 500;
  margin-top: 4px;
  text-align: center;
}

.error-icon {
  font-size: 20px;
  opacity: 0.8;
  margin-bottom: 2px;
}

/* 非激活卡片提示 */
.inactive-card-hint {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

.inactive-card-hint:hover {
  opacity: 0.8;
}

.hint-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.7;
}

.hint-text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 16px;
  font-weight: 500;
  text-align: center;
}

/* 可点击的视频项目样式 */
.video-bg.clickable {
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.video-bg.clickable:hover {
  transform: scale(1.03);
  box-shadow: 0 6px 20px rgba(0, 255, 255, 0.4);
}

/* 放大按钮覆盖层 */
.fullscreen-overlay {
  position: absolute;
  top: 5px;
  right: 5px;
  z-index: 15;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.video-bg.clickable:hover .fullscreen-overlay {
  opacity: 1;
}

.fullscreen-btn {
  width: 32px;
  height: 32px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #00FFFF;
  transition: all 0.3s ease;
}

.fullscreen-btn:hover {
  background: rgba(0, 255, 255, 0.2);
  transform: scale(1.1);
}

.fullscreen-btn svg {
  width: 18px;
  height: 18px;
}

/* 全屏弹窗样式 */
.fullscreen-modal {
  position: fixed;
  top: 0;
  left: 0;
  margin: 0 auto;
  width: 100%;
  height: 100%;
  background: #000000 !important; /* 改为完全不透明的黑色背景 */
  z-index: 99999 !important; /* 提高z-index确保在所有元素之上 */
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.fullscreen-content {
  width: 100%;
  height: 100%;
  border-radius: 12px;
  border: 1px solid rgba(0, 255, 255, 0.3);
  background: rgb(10, 15, 26) !important; /* 改为完全不透明的深蓝色背景 */
  /* backdrop-filter: blur(10px); 移除背景滤镜 */
  display: flex;
  flex-direction: column;
  position: relative;
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }

  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* 关闭按钮 */
.fullscreen-close {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 48px;
  height: 48px;
  background: rgba(255, 0, 0, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #ff6b6b;
  transition: all 0.3s ease;
  z-index: 100;
}

.fullscreen-close:hover {
  background: rgba(255, 0, 0, 0.2);
  transform: scale(1.1);
}

/* 视频信息头部 */
.fullscreen-header {
  padding: 30px 40px 20px;
  border-bottom: 1px solid rgba(0, 255, 255, 0.2);
  flex-shrink: 0;
}

.fullscreen-title {
  color: #00FFFF;
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px 0;
  font-family: 'Alibaba PuHuiTi 2.0', sans-serif;
}

.fullscreen-subtitle {
  color: rgba(255, 255, 255, 0.7);
  font-size: 16px;
  margin: 0;
  font-weight: 400;
}

/* 播放器包装容器 */
.fullscreen-player-wrapper {
  flex: 1;
  padding: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  min-height: 0;
  aspect-ratio: 16/9;
}

/* 全屏播放器 */
.fullscreen-player {
  width: 100%;
  height: 100%;
  background: #000;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.fullscreen-video {
  max-width: 100%;
  max-height: 100%;
  border-radius: 8px;
}

/* 全屏加载状态 */
.fullscreen-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #00FFFF;
  gap: 20px;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 8px;
  z-index: 10;
}

.fullscreen-loading .loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(0, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #00FFFF;
  animation: spin 1s ease-in-out infinite;
}

.fullscreen-loading .loading-text {
  font-size: 18px;
  font-weight: 500;
  margin: 0;
}

/* 全屏错误状态 */
.fullscreen-error {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #ff6b6b;
  gap: 20px;
  text-align: center;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 8px;
  z-index: 10;
}

.fullscreen-error .error-icon {
  font-size: 48px;
  opacity: 0.8;
}

.fullscreen-error .error-text {
  font-size: 18px;
  font-weight: 500;
  max-width: 400px;
  line-height: 1.5;
}

.retry-btn {
  padding: 12px 24px;
  background: rgba(0, 255, 255, 0.1);
  border: 2px solid #00FFFF;
  border-radius: 6px;
  color: #00FFFF;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.retry-btn:hover {
  background: rgba(0, 255, 255, 0.2);
  transform: translateY(-2px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .camera-popup {
    width: 95%;
    height: 85%;
  }

  .video-popup {
    width: 95%;
    height: 85%;
    max-width: none;
    max-height: none;
  }

  .camera-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .camera-info {
    margin-right: 0;
  }

  .camera-details {
    flex-wrap: wrap;
  }

  .view-btn {
    align-self: flex-end;
  }

  .fullscreen-content {
    width: 95%;
    height: 95%;
    border-radius: 8px;
  }

  .fullscreen-header {
    padding: 20px 25px 15px;
  }

  .fullscreen-title {
    font-size: 20px;
  }

  .fullscreen-subtitle {
    font-size: 14px;
  }

  .fullscreen-close {
    top: 15px;
    right: 15px;
    width: 40px;
    height: 40px;
  }

  .fullscreen-close svg {
    width: 24px;
    height: 24px;
  }

  .fullscreen-player-wrapper {
    padding: 15px;
  }
}

.card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  z-index: 6;
}

.grid-video-player {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
}

/* 移除了原来的独立返回按钮样式，现在集成在头部中 */

/* 强制下拉面板宽度与选择框等宽 */
.country-selector {
  /* 默认宽度，可被 JS 动态更新 */
  --country-option-width: 300px;
}

:deep(.country-select-popper) {
  width: var(--country-option-width) !important;
}

/* 强制下拉面板与选择框左对齐 */
:deep(.country-select-popper) {
  left: 0 !important;
}

/* 轮播导航箭头样式 */
.carousel-navigation {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  pointer-events: none;
  z-index: 50;
}

.nav-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: linear-gradient(135deg, rgba(0, 191, 255, 0.1) 0%, rgba(0, 255, 255, 0.15) 100%);
  border: 3px solid rgba(0, 255, 255, 0.4);
  border-radius: 20px;
  padding: 40px 50px;
  cursor: pointer;
  pointer-events: auto;
  transition: all 0.4s cubic-bezier(0.25, 0.1, 0.25, 1);
  backdrop-filter: blur(10px);
  box-shadow: 
    0 8px 32px rgba(0, 255, 255, 0.2),
    inset 0 1px 1px rgba(255, 255, 255, 0.1);
  user-select: none;
  min-width: 400px;
}

.nav-arrow-left {
  left: 50px;
  transform: translateY(-100%);
}

.nav-arrow-right {
  right: 50px;
  transform: translateY(-100%);
}

.nav-arrow-left:hover {
  background: linear-gradient(135deg, rgba(0, 191, 255, 0.2) 0%, rgba(0, 255, 255, 0.25) 100%);
  border-color: rgba(0, 255, 255, 0.7);
  // transform: translateY(-50%) scale(1.05);
  box-shadow: 
    0 12px 40px rgba(0, 255, 255, 0.3),
    inset 0 1px 1px rgba(255, 255, 255, 0.2);
}

.nav-arrow-right:hover {
  background: linear-gradient(135deg, rgba(0, 191, 255, 0.2) 0%, rgba(0, 255, 255, 0.25) 100%);
  border-color: rgba(0, 255, 255, 0.7);
  // transform: translateY(-50%) scale(1.05);
  box-shadow: 
    0 12px 40px rgba(0, 255, 255, 0.3),
    inset 0 1px 1px rgba(255, 255, 255, 0.2);
}

.arrow-content {
  display: flex;
  align-items: center;
  gap: 30px;
  color: #00FFFF;
}

.nav-arrow-right .arrow-content {
  flex-direction: row-reverse;
}

.arrow-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 96px;
  height: 96px;
  background: rgba(0, 255, 255, 0.15);
  border-radius: 50%;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.nav-arrow:hover .arrow-icon {
  background: rgba(0, 255, 255, 0.25);
  transform: scale(1.1);
}

.arrow-icon svg {
  color: #00FFFF;
  filter: drop-shadow(0 0 8px rgba(0, 255, 255, 0.5));
  width: 64px;
  height: 64px;
}

.arrow-label {
  display: flex;
  flex-direction: column;
  gap: 8px;
  text-align: left;
  min-width: 200px;
}

.nav-arrow-right .arrow-label {
  text-align: right;
}

.label-text {
  font-family: 'Alibaba PuHuiTi 2.0', sans-serif;
  font-size: 28px;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.8);
  text-shadow: 0 0 4px rgba(0, 255, 255, 0.3);
}

.card-name {
  font-family: 'Alibaba PuHuiTi 2.0', sans-serif;
  font-size: 36px;
  font-weight: 600;
  color: #00FFFF;
  text-shadow: 0 0 12px rgba(0, 255, 255, 0.6);
  white-space: nowrap;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .nav-arrow {
    min-width: 320px;
    padding: 32px 40px;
  }
  
  .nav-arrow-left {
    left: 30px;
  }
  
  .nav-arrow-right {
    right: 30px;
  }
  
  .arrow-content {
    gap: 24px;
  }
  
  .arrow-icon {
    width: 80px;
    height: 80px;
  }
  
  .arrow-icon svg {
    width: 52px;
    height: 52px;
  }
  
  .label-text {
    font-size: 24px;
  }
  
  .card-name {
    font-size: 30px;
  }
  
  .arrow-label {
    min-width: 160px;
  }
}

@media (max-width: 768px) {
  .nav-arrow {
    min-width: 240px;
    padding: 24px 30px;
  }
  
  .nav-arrow-left {
    left: 20px;
  }
  
  .nav-arrow-right {
    right: 20px;
  }
  
  .arrow-content {
    gap: 16px;
  }
  
  .arrow-icon {
    width: 64px;
    height: 64px;
  }
  
  .arrow-icon svg {
    width: 40px;
    height: 40px;
  }
  
  .label-text {
    font-size: 20px;
  }
  
  .card-name {
    font-size: 24px;
  }
  
  .arrow-label {
    min-width: 120px;
  }
}
</style>
